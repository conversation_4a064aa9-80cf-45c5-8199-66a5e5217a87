DATABASE_URL=${SERVICE_DATABASE_ENDPOINT}

MONGO_DB_URI=${SERVICE_MONGODB_ENDPOINT}
MONGO_DB_NAME=${SERVICE_MONGODB_DATABASE}

WWW_URL=${SERVICE_WEB_ENDPOINT}
FALCON_URL=${SERVICE_FALCON_ENDPOINT}

ROADRUNNER_NUM_WORKERS=0
ROADRUNNER_DEBUG=false

TEMPORAL_NUM_WORKERS=0
TEMPORAL_ADDRESS=${SERVICE_TEMPORAL_ENDPOINT}
TEMPORAL_NAMESPACE=${SERVICE_TEMPORAL_NAMESPACE}

S3_REGION=${S3_REGION}
S3_VERSION=${S3_VERSION}
S3_MEDIA_WRITE_BUCKET=${S3_MEDIA_WRITE_BUCKET}
S3_MEDIA_READ_BUCKET=${S3_MEDIA_READ_BUCKET}

AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-false}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-false}

SOCKET_URL=http://${SERVICE_CENTRIFUGO_ENDPOINT}/api
SOCKET_API_KEY=${SOCKET_API_KEY}
SOCKET_SECRET_KEY=${SOCKET_SECRET_KEY}

CLOUDFRONT_MEDIA_DISTRIBUTION_ID=${CLOUDFRONT_MEDIA_DISTRIBUTION_ID:-false}
