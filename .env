SERVICE_API_ENDPOINT=http://clubster-api-nginx
SERVICE_FALCON_ENDPOINT=https://cardinal.dev.clubster.com:3255
SERVICE_NIGHTINGALE_ENDPOINT=http://clubster-nightingale:3255
SERVICE_MOCKINGBIRD_ENDPOINT=wss://cardinal.dev.clubster.com:6625
SERVICE_WEB_ENDPOINT=https://www.cardinal.dev.clubster.com/
SERVICE_CENTRIFUGO_ENDPOINT=cardinal.dev.clubster.com:8000
SERVICE_DATABASE_ENDPOINT=pgsql://cardinal:<EMAIL>:5432/cardinal
SERVICE_TIMESCALE_ENDPOINT=pgsql://clubster:clubster@timescale:5432/clubster
SERVICE_REDIS_HOST=clubster-redis
SERVICE_REDIS_ENDPOINT=redis://clubster-redis
SERVICE_MONGODB_ENDPOINT=***********************************************************
SERVICE_MONGODB_DATABASE=clubster-dev
SERVICE_TEMPORAL_ENDPOINT=frontend.temporal.clubster.com:7233
SERVICE_TEMPORAL_NAMESPACE=cardinal

DEBUG_CLUBSTER_API=false
DEBUG_CLUBSTER_FALCON=true
DEBUG_CLUBSTER_MOCKINGBIRD=false
DEBUG_CLUBSTER_NIGHTINGALE=false
DEBUG_CLUBSTER_PUFFIN=false
DEBUG_CLUBSTER_WEB=true

XDEBUG_CONFIG="idekey=PHPSTORM client_host=************* client_port=9003"

AWS_REGION=us-east-2
AWS_ACCOUNT_ID=************

S3_REGION=us-east-2
S3_VERSION=latest
S3_MEDIA_WRITE_BUCKET=upload.media.clubster.com
S3_MEDIA_READ_BUCKET=media.clubster.com

CLUBSTER_API_INTERNAL_DNS=clubster-api
CLUBSTER_WEB_INTERNAL_DNS=clubster-web

TEMPORAL_FRONTEND_URL=http://temporal.clubster.com:7243
TEMPORAL_NAMESPACE=cardinal
TEMPORAL_QUEUE=default

CLOUDFRONT_MEDIA_DISTRIBUTION_ID=ETS2CXM6YM9VB

SOCKET_API_KEY=f31891d5-1854-4326-9e6f-3d12f544e256
SOCKET_SECRET_KEY=c390137f-eeb4-466e-9e06-4201e4fe41c8

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=JMFhiuFx78mEziPl8J6h/vSJ+fYcO2mRd33k+Oiv

APP_ENV=dev
API_ENV=cardinal
