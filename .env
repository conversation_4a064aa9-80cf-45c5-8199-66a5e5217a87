SERVER_IP=0.0.0.0
SERVER_PORT=6625
SERVER_ENV=cardinal
DEBUG_CLUBSTER_MOCKINGBIRD=${DEBUG_CLUBSTER_MOCKINGBIRD:-false}
XDEBUG_CONFIG=${XDEBUG_CONFIG:-false}
ROLLBAR_TOKEN=2e04395469da427ca83c8ecff562b958
REDIS_HOST=${SERVICE_REDIS_HOST}
REDIS_PORT=6379
REDIS_DB=2
MONGO_DB_URI=${SERVICE_MONGODB_ENDPOINT}
MONGO_DB_NAME=${SERVICE_MONGODB_DATABASE}
DATABASE_URL=${SERVICE_DATABASE_ENDPOINT}

FALCON_URL=${SERVICE_FALCON_ENDPOINT}
PHP_IDE_CONFIG="serverName=clubster-mockingbird"

AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-false}
AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-false}
