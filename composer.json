{"name": "clubster/nightingale", "description": "a high performance notification queue", "minimum-stability": "stable", "license": "proprietary", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "react/http": "^1.9", "monolog/monolog": "^3.4", "wyrihaximus/react-psr-3-stdio": "^3.0", "react/async": "^4.1", "aws/aws-sdk-php": "^3.277", "symfony/dotenv": "^6.3", "mongodb/mongodb": "1.13.1", "nesbot/carbon": "^2.68", "ramsey/uuid": "^4.7", "twig/twig": "^3.7", "react/event-loop": "^1.4.0", "symfony/dependency-injection": "^6.3", "symfony/config": "^6.3", "drift/dbal": "^0.1.5", "voryx/pgasync": "^2.0", "sentry/sentry": "^4.9", "ext-mongodb": "*", "promphp/prometheus_client_php": "^2.11"}, "autoload": {"psr-4": {"Clubster\\Nightingale\\": "src/"}}}