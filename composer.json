{"name": "clubster/puffin", "description": "Puffin is an application for building asynchronous statistics services.", "minimum-stability": "stable", "license": "proprietary", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "react/http": "^1.9", "monolog/monolog": "^3.4", "react/async": "^4.1", "symfony/dotenv": "^6.3", "mongodb/mongodb": "^1.13", "nesbot/carbon": "^2.69", "react/event-loop": "^1.4", "symfony/config": "^6.3", "symfony/dependency-injection": "^6.3", "aws/aws-sdk-php": "^3.280", "drift/dbal": "^0.1.5", "promphp/prometheus_client_php": "^2.11"}, "autoload": {"psr-4": {"Clubster\\Puffin\\": "src/"}}}