<?php

namespace Clubster\Falcon\Task\Event\Statistics;

use Doctrine\DBAL\ParameterType;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class LoadEventStatistics extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $eventId = $this->getData('eventId');

            $sql = 'WITH anchor AS (
  SELECT eo.id, eo.event_family, eo.occurrence_index, eo.status, eo.publish_at,
				p.id AS post_id,
         p.subject, p.created_at, p.delivery_method
  FROM event_occurrences eo
  JOIN posts p ON p.id = eo.event_id
  WHERE eo.id = ?
),
post_ids AS (
  SELECT p.id
  FROM posts p
  JOIN anchor a ON p.event_id = a.id
),
notification_ids AS (
  SELECT pn.id
  FROM notifications.post_notifications pn
  JOIN post_ids pi ON pn.post_id = pi.id
),

-- Notifications (count by delivery method)
notification_count AS (
  SELECT a.id AS event_occurrence_id,
         COUNT(n.id)                           AS notification_count,
         COUNT(*) FILTER (WHERE n.delivery_method = \'email\') AS email_notification_count,
         COUNT(*) FILTER (WHERE n.delivery_method = \'push\')  AS push_notification_count
  FROM anchor a
  LEFT JOIN notification_ids ni ON TRUE
  LEFT JOIN notifications.notifications n ON n.id = ni.id
  GROUP BY a.id
),

-- Audience/subscriptions (deleted_at filtered at index)
audience_count AS (
  SELECT a.id AS event_occurrence_id,
         COUNT(s.id)                                                     AS subscriber_count,
         COUNT(*) FILTER (WHERE s.allow_email)                           AS email_subscriber_count,
         COUNT(*) FILTER (WHERE s.allow_push)                            AS push_subscriber_count
  FROM anchor a
  JOIN posts.post_audience pa
    ON pa.post_id = a.post_id AND pa.target_type = \'organization\'
  JOIN organizations.subscriptions s
    ON s.organization_id = pa.target_id::uuid AND s.deleted_at IS NULL
  GROUP BY a.id
),

-- Views
post_views AS (
  SELECT a.id AS event_occurrence_id, COUNT(*) AS post_views
  FROM anchor a
  JOIN post_ids pi ON TRUE
  JOIN posts.views v ON v.post_id = pi.id
  GROUP BY a.id
),

-- Pre-aggregate events for the small set of notification_ids, then roll up once.
email_events AS (
  SELECT e.notification_id,
         e.event_type,
         COUNT(*) AS c
  FROM notifications.events e
  JOIN notification_ids ni
    ON e.notification_id = ni.id
  GROUP BY e.notification_id, e.event_type
),

email_stats AS (
  SELECT a.event_occurrence_id,
         SUM(c) FILTER (WHERE event_type = \'Send\')     AS sent_count,
         SUM(c) FILTER (WHERE event_type = \'Delivery\') AS delivery_count,
         SUM(c) FILTER (WHERE event_type = \'Open\')     AS open_count,
         SUM(c) FILTER (WHERE event_type = \'Click\')    AS click_count
  FROM anchor a
  JOIN email_events ee ON TRUE
  GROUP BY a.event_occurrence_id
)

-- Family occurrence stats (use composite index)
occurrence_stats AS (
  SELECT a.id AS event_occurrence_id,
         a.occurrence_index AS event_occurrence_index,
         COUNT(f.*)                                            AS event_total_occurrences,
         COUNT(*) FILTER (WHERE f.start_at < now())            AS event_occurrence_past_occurrences,
         COUNT(*) FILTER (WHERE f.start_at > now())            AS event_occurrence_future_occurrences
  FROM anchor a
  JOIN event_occurrences f ON f.event_family = a.event_family
  GROUP BY a.id, a.occurrence_index
)

SELECT
  a.id                      AS event_occurrence_id,
  os.event_occurrence_index,
  COALESCE(os.event_total_occurrences,0)                 AS event_total_occurrences,
  COALESCE(os.event_occurrence_future_occurrences,0)     AS event_occurrence_future_occurrences,
  COALESCE(os.event_occurrence_past_occurrences,0)       AS event_occurrence_past_occurrences,
  a.subject                AS post_subject,
  a.created_at             AS post_created_at,
  a.delivery_method        AS post_delivery_methods,
  a.status                 AS post_status,
  a.publish_at             AS post_publish_at,
  COALESCE(nc.notification_count,0)                      AS notification_count,
  COALESCE(nc.email_notification_count,0)                AS email_notification_count,
  COALESCE(nc.push_notification_count,0)                 AS push_notification_count,
  COALESCE(ac.subscriber_count,0)                        AS total_audience_count,
  COALESCE(ac.email_subscriber_count,0)                  AS email_audience_count,
  COALESCE(ac.push_subscriber_count,0)                   AS push_audience_count,
  COALESCE(pv.post_views,0)                              AS post_views,
  COALESCE(es.sent_count,0)                              AS sent_count,
  COALESCE(es.delivery_count,0)                          AS delivery_count,
  COALESCE(es.open_count,0)                              AS open_count,
  COALESCE(es.click_count,0)                             AS click_count
FROM anchor a
LEFT JOIN notification_count nc ON nc.event_occurrence_id = a.id
LEFT JOIN audience_count     ac ON ac.event_occurrence_id = a.id
LEFT JOIN post_views         pv ON pv.event_occurrence_id = a.id
LEFT JOIN email_stats        es ON es.event_occurrence_id = a.id
LEFT JOIN occurrence_stats   os ON os.event_occurrence_id = a.id
LIMIT 1;
';

            $postStatistics = $this->connection()->executeQuery($sql, [
                $eventId,
            ], [
                ParameterType::STRING,
            ]);

            $postStatistics = $postStatistics->fetchAssociative();
            $postStatistics['post_delivery_methods'] = json_decode($postStatistics['post_delivery_methods'], true);

            return $postStatistics;

        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'eventId' => new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
