<?php

namespace Clubster\Falcon\Task\Post\Builder;

use Clubster\Falcon\Infrastructure\Task\CallableTask;
use Exception;
use HtmlSanitizer\Sanitizer;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Validator\Constraints;
use function React\Promise\map;

class ProcessBodyText extends CallableTask
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $inputTitle = $this->getData('title', '');
            $cleanTitle = Sanitizer::create(['max_input_length' => 2000])->sanitize($inputTitle);
            $cleanTitle = html_entity_decode($cleanTitle,ENT_QUOTES);

            $inputBody = $this->getData('body', '');
            $cleanBody = Sanitizer::create(['max_input_length' => 200000])->sanitize($inputBody);
            $cleanBody = html_entity_decode($cleanBody, ENT_QUOTES);

            $cleanBody = trim($cleanBody);

            $cleanBody = $this->clearVerticalSpace($cleanBody);

            $urlRegex = '(?<urls>((https?|ftp|file):\/\/|www\.)[-A-Z0-9+&@#\/%?=~_|!:,.;]*[A-Z0-9+&@#\/%=~_|])';
            $emailRegex = '(?<emails>(?:mailto:)?([A-Z0-9._%+-]+@(?:[A-Z0-9-]+\.)+[A-Z]{2,}))';
            $regex = '/((?<=\s|\n|\r|^)\b'.$urlRegex.')|((?<=\s|\n|\r|^)\b'.$emailRegex.'\b)/i';

            preg_match_all($regex, $cleanBody, $result, PREG_OFFSET_CAPTURE);
            $links = [];
            if (!empty($result)) {
                $urls = array_map(function($url) {
                    $length = strlen($url[0]);
                    if (strpos($url[0], 'http') !== 0) {
                        $url[0] = 'https://'.$url[0];
                    }
                    return [
                        'url' => $url[0],
                        'position' => $url[1],
                        'length' => $length,
                        'type'=>'url'
                    ];
                }, array_filter($result['urls'], fn($url) => !empty($url[0])));

                $emails = array_map(function($email) {
                    return [
                        'url' => $email[0],
                        'position' => $email[1],
                        'length' => strlen($email[0]),
                        'type'=>'email'
                    ];
                }, array_filter($result['emails'], fn($email) => !empty($email[0])));

//                if (count($urls) > 0) {
//                    map($urls, function ($url) {
//                        $url['preview'] = $this->container->get(ProcessLinkPreviews::class)($url);
//                        unset($url[0],$url[1]);
//                        return $url;
//                    })->done(function ($data) use (&$urls) {
//                        $urls = $data;
//                    });
//                }

                $links = array_merge($urls, $emails);
            }


            $deleteLinks = $this->connection()->createQueryBuilder();
            $deleteLinks->delete('posts.links')
                ->where('post_id = '.$deleteLinks->createNamedParameter($this->getData('id')))
                ->execute();

            foreach ($links as $link) {
                $linkInsert = $this->connection()->createQueryBuilder();
                $linkInsert->insert('posts.links');
                $linkInsert->setValue('id', $linkInsert->createNamedParameter((string)Uuid::uuid4()));
                $linkInsert->setValue('post_id', $linkInsert->createNamedParameter($this->getData('id')));
                $linkInsert->setValue('created_by_id', $linkInsert->createNamedParameter($this->getData('userId')));
                $linkInsert->setValue('updated_by_id',$linkInsert->createNamedParameter($this->getData('userId')));
                $linkInsert->setValue('url',$linkInsert->createNamedParameter($link['url']));
                $linkInsert->setValue('offset_start',$linkInsert->createNamedParameter($link['position']));
                $linkInsert->setValue('offset_end',$linkInsert->createNamedParameter(bcadd($link['position'], $link['length'])));
                $linkInsert->setValue('length',$linkInsert->createNamedParameter($link['length']));
                $linkInsert->setValue('created_at', 'NOW()');
                $linkInsert->setValue('updated_at', 'NOW()');
                $linkInsert->setValue('link_type',ucwords($linkInsert->createNamedParameter($link['type'])));
                $linkInsert->execute();
            }

            $this->mergeData([
                'title'=>$cleanTitle,
                'body'=>$cleanBody,
                'links'=>$links
            ]);

            return $this->getData();
        } catch (\Throwable $exception) {
            throw $exception;
        }
    }

    private function clearVerticalSpace(string $html): string
    {
        libxml_use_internal_errors(true);

        $wrapped = '<div id="__wrapper__">' . $html . '</div>';
        $dom     = new \DOMDocument();
        $dom->loadHTML(
            mb_convert_encoding($wrapped, 'HTML-ENTITIES', 'UTF-8'),
            \LIBXML_HTML_NOIMPLIED | \LIBXML_HTML_NODEFDTD
        );
        libxml_clear_errors();

        $xpath = new \DOMXPath($dom);
        $root  = $dom->getElementById('__wrapper__');

        $isEmpty = function(\DOMNode $n) {
            $txt = preg_replace('/[\\s\\x{00A0}]+/u', '', $n->textContent);
            return $txt === '';
        };

        while ($root->firstChild instanceof \DOMElement
            && in_array($root->firstChild->tagName, ['p','div','br'], true)
            && $isEmpty($root->firstChild)
        ) {
            $root->removeChild($root->firstChild);
        }

        while ($root->lastChild instanceof \DOMElement
            && in_array($root->lastChild->tagName, ['p','div','br'], true)
            && $isEmpty($root->lastChild)
        ) {
            $root->removeChild($root->lastChild);
        }

        foreach ($xpath->query('//p') as $p) {
            if ($p->firstChild instanceof \DOMElement
                && $p->firstChild->tagName === 'br'
            ) {
                $p->removeChild($p->firstChild);
            }
        }

        $clean = '';
        foreach ($root->childNodes as $child) {
            $clean .= $dom->saveHTML($child);
        }

        return $this->cleanUtf8KeepEmojis($clean);
    }

    private function cleanUtf8KeepEmojis(string $input): string
    {   // iconv with //IGNORE will remove only invalid fragments
        $clean = @iconv('UTF-8', 'UTF-8//IGNORE', $input);
        // optional: turn non-breaking spaces (U+00A0) into plain spaces
        return str_replace("\xC2\xA0", ' ', $clean);
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'id'=>new Constraints\Required(),
                'title'=>new Constraints\Required(),
                'body'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
