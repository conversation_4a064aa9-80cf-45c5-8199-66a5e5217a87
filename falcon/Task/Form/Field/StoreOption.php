<?php
namespace Clubster\Falcon\Task\Form\Field;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class StoreOption extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $user = $this->request->getRequestUser();
            $option = $this->getData('option');
            $formFieldId = $this->getData('formFieldId');
            $createOptionSql = $this->connection()->createQueryBuilder();
            $result = $createOptionSql->insert('forms.form_options')
                ->setValue('id', $createOptionSql->createNamedParameter($option->getData('id')))
                ->setValue('form_field_id', $createOptionSql->createNamedParameter($formFieldId))
                ->setValue('label', $createOptionSql->createNamedParameter($option->getData('label')))
                ->setValue('value', $createOptionSql->createNamedParameter($option->getData('value')))
                ->setValue('active', $createOptionSql->createNamedParameter($option->getData('active', true, false), \Doctrine\DBAL\ParameterType::BOOLEAN))
                ->setValue('ordinal', $createOptionSql->createNamedParameter($option->getData('ordinal')))
                ->setValue('created_by', $createOptionSql->createNamedParameter($user))
                ->setValue('updated_by', $createOptionSql->createNamedParameter($user))
                ->setValue('created_at', 'NOW()')
                ->setValue('updated_at', 'NOW()')
                ->execute();

            return $result;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
