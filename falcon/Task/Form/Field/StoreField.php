<?php
namespace Clubster\Falcon\Task\Form\Field;

use Clubster\Falcon\Infrastructure\Data\DataObject;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class StoreField extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $user = $this->request->getRequestUser();
            $element = $this->getData('element');
            $sectionId = $this->getData('sectionId', null);
            $createElementSql = $this->connection()->createQueryBuilder();
            $result = $createElementSql->insert('forms.form_fields')
                ->setValue('id', $createElementSql->createNamedParameter($element->getData('id')))
                ->setValue('label', $createElementSql->createNamedParameter($element->getData('label')))
                ->setValue('help_text', $createElementSql->createNamedParameter($element->getData('help_text',null)))
                ->setValue('type', $createElementSql->createNamedParameter($element->getData('type')))
                ->setValue('required', $createElementSql->createNamedParameter($element->getData('required',false, false), \Doctrine\DBAL\ParameterType::BOOLEAN))
                ->setValue('minimum_length', $createElementSql->createNamedParameter($element->getData('minimum_length',null)))
                ->setValue('maximum_length', $createElementSql->createNamedParameter($element->getData('maximum_length',null)))
                ->setValue('accessibility', $createElementSql->createNamedParameter($element->getData('accessibility','public')))
                ->setValue('form_id', $createElementSql->createNamedParameter($this->getData('formId')))
                ->setValue('ordinal', $createElementSql->createNamedParameter($element->getData('ordinal',0)))
                ->setValue('allow_multiple', $createElementSql->createNamedParameter($element->getData('allow_multiple',false, false), \Doctrine\DBAL\ParameterType::BOOLEAN))
                ->setValue('minimum_count', $createElementSql->createNamedParameter($element->getData('minimum_count',null)))
                ->setValue('maximum_count', $createElementSql->createNamedParameter($element->getData('maximum_count',null)))
                ->setValue('created_by', $createElementSql->createNamedParameter($user))
                ->setValue('updated_by', $createElementSql->createNamedParameter($user))
                ->setValue('created_at', 'NOW()')
                ->setValue('updated_at', 'NOW()')
                ->setValue('form_section_id', $createElementSql->createNamedParameter($sectionId))
                ->execute();

            if ($element->hasData('options')) {
                $options = array_map(fn($option) => DataObject::create($option), $this->getData('options'));
                foreach ($options as $option) {
                    $this->container->get(StoreOption::class)->setData([
                        'option'=>$option,
                        'formFieldId'=>$element->getData('id')
                    ])();
                }
            }

            return $result;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
