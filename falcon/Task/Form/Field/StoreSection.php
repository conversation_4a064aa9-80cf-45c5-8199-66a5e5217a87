<?php
namespace Clubster\Falcon\Task\Form\Field;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class StoreSection extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $user = $this->request->getRequestUser();
            $section = $this->getData('section');
            $createSectionSql = $this->connection()->createQueryBuilder();
            $result = $createSectionSql->insert('forms.form_sections')
                ->setValue('id', $createSectionSql->createNamedParameter($section->getData('id')))
                ->setValue('label', $createSectionSql->createNamedParameter($section->getData('label')))
                ->setValue('description', $createSectionSql->createNamedParameter($section->getData('description',null)))
                ->setValue('ordinal', $createSectionSql->createNamedParameter($section->getData('ordinal',0)))
                ->setValue('created_by', $createSectionSql->createNamedParameter($user))
                ->setValue('updated_by', $createSectionSql->createNamedParameter($user))
                ->setValue('created_at', 'NOW()')
                ->setValue('updated_at', 'NOW()')
                ->setValue('form_id', $createSectionSql->createNamedParameter($this->getData('formId')))
                ->execute();

            return $result;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
