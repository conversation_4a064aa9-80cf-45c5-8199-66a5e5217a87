<?php
namespace Clubster\Falcon\Task\Form\Record;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class Load extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $recordRecallCode = $this->getData('recordRecallCode');
            $loadFormRecordQuery = $this->connection()->createQueryBuilder();
            $loadFormRecordQuery->select('data')
                ->from('forms.records')
                ->where('record_recall_code = '.$loadFormRecordQuery->createNamedParameter($recordRecallCode))
                ->orderBy('created_at', 'DESC')
                ->setMaxResults(1);

            $response = $loadFormRecordQuery->executeQuery()->fetchOne();

            return json_decode($response, true);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
