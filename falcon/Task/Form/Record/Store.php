<?php
namespace Clubster\Falcon\Task\Form\Record;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Validator\Constraints;

class Store extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            if (!$this->getData('_meta.recordRecallCode', false)) {
                return ['success' => false, 'message' => 'No record recall code provided'];
            }
            $user = $this->request->getRequestUser();
            $storeFormRecordQuery = $this->connection()->createQueryBuilder();
            $storeFormRecordQuery->insert('forms.records')
                ->setValue('id',Uuid::uuid4()->toString())
                ->setValue('form_id',$storeFormRecordQuery->createNamedParameter($this->getData('form.id')))
                ->setValue('data',$storeFormRecordQuery->createNamedParameter(json_encode($this->getData('data',[]))))
                ->setValue('record_recall_code',$storeFormRecordQuery->createNamedParameter($this->getData('_meta.recordRecallCode')))
                ->setValue('created_at',$storeFormRecordQuery->createNamedParameter($this->getData('_meta.submittedAt')))
                ->setValue('created_by',$storeFormRecordQuery->createNamedParameter($user))
                ->setValue('submitted_from',$storeFormRecordQuery->createNamedParameter($this->getData('_meta.submittedFrom')))
                ->executeQuery();

            $response = $storeFormRecordQuery->executeQuery();

            return $response;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
