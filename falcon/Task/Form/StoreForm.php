<?php
namespace Clubster\Falcon\Task\Form;

use Clubster\Falcon\Infrastructure\Data\DataObject;
use Clubster\Falcon\Task\Form\Field\StoreField;
use Clubster\Falcon\Task\Form\Field\StoreSection;
use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class StoreForm extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $user = $this->request->getRequestUser();
            // create the form record
            // if there's an id check if it exists
            // if the form exists we start updates
            // if the form is not in the database we create it
                // new forms are not able to existing fields
            $this->connection()->beginTransaction();

            $existingFormSql = $this->connection()->createQueryBuilder();
            $existingForm = $existingFormSql->select('*')
                ->from('forms.form')
                ->where('id = '. $existingFormSql->createNamedParameter($this->getData('form.id')))
                ->setMaxResults(1)
                ->execute()
                ->fetchAssociative();

            if (empty($existingForm)) {
                // create form
                $createFormSql = $this->connection()->createQueryBuilder();
                $createFormSql->insert('forms.form')
                    ->setValue('id', $createFormSql->createNamedParameter($this->getData('form.id')))
                    ->setValue('name', $createFormSql->createNamedParameter($this->getData('form.name')))
                    ->setValue('description', $createFormSql->createNamedParameter($this->getData('form.description')))
                    ->setValue('created_by', $createFormSql->createNamedParameter($user))
                    ->setValue('updated_by', $createFormSql->createNamedParameter($user))
                    ->setValue('created_at', 'NOW()')
                    ->setValue('updated_at', 'NOW()');

                if (!empty($this->getData('form.activate_at', false))) {
                    $createFormSql->setValue('activate_at', $createFormSql->createNamedParameter($this->getData('form.activate_at')));
                }
                if (!empty($this->getData('form.deactivate_at', false))) {
                    $createFormSql->setValue('deactivate_at', $createFormSql->createNamedParameter($this->getData('form.deactivate_at')));
                }

                $createFormSql->execute();

                foreach ($this->getData('form.actions', []) as $action) {
                    $action['config'] = json_encode($action['config'], JSON_THROW_ON_ERROR);
                    $createFormActionSql = $this->connection()->createQueryBuilder();
                    $createFormActionSql->insert('forms.form_actions')
                        ->setValue('id', $createFormActionSql->createNamedParameter($action['id']))
                        ->setValue('form_id', $createFormActionSql->createNamedParameter($this->getData('form.id')))
                        ->setValue('type', $createFormActionSql->createNamedParameter($action['type']))
                        ->setValue('config', $createFormActionSql->createNamedParameter($action['config']))
                        ->setValue('created_by', $createFormActionSql->createNamedParameter($user))
                        ->setValue('updated_by', $createFormActionSql->createNamedParameter($user))
                        ->setValue('created_at', 'NOW()')
                        ->setValue('updated_at', 'NOW()');

                    $createFormActionSql->execute();
                }

                $elements = array_map(fn($element) => DataObject::create($element), $this->getData('form.elements'));
                foreach ($elements as $element) {
                    if ($element->hasData('fields')) {
                        // this is a section
                        $this->container->get(StoreSection::class)->setData([
                            'section'=>$element,
                            'formId'=>$this->getData('form.id')
                        ])();
                        $sectionId = $element->get('id');
                        foreach ($element->get('fields') as $field) {
                            $this->container->get(StoreField::class)->setData([
                                'element'=>$field,
                                'sectionId'=>$sectionId,
                                'formId'=>$this->getData('form.id')
                            ])();
                        }
                        continue;
                    }

                    $this->container->get(StoreField::class)->setData([
                        'element'=>$element,
                        'formId'=>$this->getData('form.id')
                    ])();
                }
            }
            $this->connection()->commit();
        } catch (Exception $exception) {
            if ($this->connection()->isTransactionActive()) {
                $this->connection()->rollBack();
            }
            throw $exception;
        }
    }



    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
