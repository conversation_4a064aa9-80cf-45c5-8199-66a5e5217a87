<?php
namespace Clubster\Falcon\Task\Form;

use Exception;
use Clubster\Falcon\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class LoadFormList extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $formQuery = $this->connection()->createQueryBuilder();
            $formQuery->select([
                'id',
                'name',
                'description',
                'active_at',
                'deactivate_at',
                'created_at',
                'updated_at',
                'deleted_at',
            ])
                ->from('forms.form', 'form')
                ->where('form.deleted_at IS NULL');
            $forms = $formQuery->execute()->fetchAllAssociative();

            return $forms;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
