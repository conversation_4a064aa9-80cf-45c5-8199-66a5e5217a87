<?php

namespace Clubster\Falcon\Infrastructure\Data;

use Clubster\Falcon\Infrastructure\Logger\Logger;
use Exception;
use InvalidArgumentException;
use ReflectionClass;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\Validator\Constraints\Collection;

/**
 * Trait DataAware
 *
 * This trait provides the ability to accept and handle input data
 *
 * @package SingleComm\Lib\Traits
 */
trait DataAware
{
    /**
     * An array of data
     * @var mixed
     */
    protected $_data = [];
    protected $_rawData = [];
    /**
     * @var DataProcessorInterface[]
     */
    protected $dataProcessors = [];

    /**
     * Log a message
     * @See \SingleComm\Lib\Traits\LoggerAware::log()
     */
    abstract protected function log(string $message, ?int $level = null): void;

    /**
     * Return an array of default data.
     *
     * @return array
     */
    protected function defineDataDefaults(): ?array
    {
        return [];
    }

    /**
     * Apply transformations to input data prior to being validated
     *
     * @param mixed $data The data to transform
     * @return mixed
     */
    protected function preValidationTransform($data)
    {
        return $data;
    }

    /**
     * Apply transformations to input data after data is validated
     *
     * @param mixed $data The data to transform
     * @return mixed
     */
    protected function postValidationTransform($data)
    {
        return $data;
    }

    /**
     * Apply transformations to output data prior to being sent
     *
     * @param mixed $data The data to transform
     * @return mixed
     */
    protected function preResponseTransform($data)
    {
        return $data;
    }

    /**
     * Check for the existence of a data item
     *
     * @param string $key The data key to check
     *
     * @return bool Whether the data item exists
     */
    public function hasData($key, $allowEmpty=true): bool
    {
        try {
            $data = $this->dataAccessor($key);
            if (!$allowEmpty && empty($data) && !is_bool($data)) {
                return false;
            }
            return true;
        }catch (Exception $exception) {
            return false;
        }
    }

    /**
     * Check for the existence of a raw data item
     *
     * @param string $key The raw data key to check
     *
     * @return bool Whether the raw data item exists
     */
    public function hasRawData($key): bool
    {
        try {
            $this->dataAccessor($key, $this->_rawData);
            return true;
        }catch (Exception $exception) {
            return false;
        }
    }

    /**
     * Retrieve a data item.
     *
     * This first parameter can be a string or an array.
     * If a string is specified, it will be used as the key
     * of the data element. Dot notation can be used to retrieve
     * data from multidimensional data items. If an array is used
     * each value in the array will be used as the key of a data
     * element and an array will be returned with the original
     * Not specifying a key will return the full data array.
     *
     * The second parameter "default" can be used to return
     * a default value when the requested key is not found.
     *
     * @param string|array $key The data to retrieve.
     * @param mixed $default The default value if the requested key does not exist
     *
     * @return mixed|null
     */
    public function getData($key = null, $default = null, $allowEmpty = true)
    {
        // return an array of data based on the structure of the "key" array
        if (is_array($key)) {
            $outputData = [];
            foreach ($key as $outputKey=>$dataKey) {
                $outputDefault = is_array($default) && array_key_exists($outputKey, $default)?$default[$outputKey]:$default;
                $outputData[$outputKey] = $this->getData($dataKey, $outputDefault);
            }

            return $outputData;
        }

        if ($this->hasData($key, $allowEmpty)) {
            return $this->dataAccessor($key);
        }

        // check to see if a default was provided
        if (func_num_args() === 1) {
            throw new InvalidArgumentException(sprintf('The data item "%s" can not be found in %s. Check your payload, or provide a default value.', $key, get_called_class()));
        }

        // if no key was provided return the full array
        if (null === $key && null === $default) {
            return $this->_data;
        }

        // return the default if no previous access method was successful
        return $default;
    }

    public function getRawData($key = null, $default = null)
    {
        // return an array of data based on the structure of the "key" array
        if (is_array($key)) {
            $outputData = [];
            foreach ($key as $outputKey=>$dataKey) {
                $outputDefault = is_array($default) && array_key_exists($outputKey, $default)?$default[$outputKey]:$default;
                $outputData[$outputKey] = $this->getRawData($dataKey, $outputDefault);
            }

            return $outputData;
        }

        if ($this->hasRawData($key)) {
            return $this->dataAccessor($key, $this->_rawData);
        }

        // check to see if a default was provided
        if (func_num_args() === 1) {
            throw new InvalidArgumentException(sprintf('The raw data item "%s" can not be found in %s. Check your payload, or provide a default value.', $key, get_called_class()));
        }

        // if no key was provided return the full array
        if (null === $key && null === $default) {
            return $this->_data;
        }

        // return the default if no previous access method was successful
        return $default;
    }

    /**
     * Sets the data for the class. The optional second
     * parameter, normalizeDataKeys, allows for all data
     * keys to be normalized into camelCase
     *
     * @param array $data The data
     * @param bool $normalizeDataKeys Flag used to normalize data keys
     * @param bool $applyDefaults Flag used to enable applying defaults
     * @return DataAware
     */
    public function setData(array $data, $normalizeDataKeys = true, $applyDefaults = true, $castStringBooleans=true)
    {
        $this->_rawData = $data;
        $data = $this->processData($data, $normalizeDataKeys, $applyDefaults, $castStringBooleans);
        $this->_data = $data;
        return $this;
    }

    /**
     * Merges the data with the already existing data for the class.
     * The optional second parameter, normalizeDataKeys, allows for
     * all data keys to be normalized into camelCase
     *
     * @param array $data The data
     * @param bool $normalizeDataKeys Flag used to normalize data keys
     * @param bool $applyDefaults Flag used to enable applying defaults
     * @return DataAware
     */
    public function mergeData(array $data, $normalizeDataKeys = true, $applyDefaults = false, $castStringBooleans=true)
    {
        $this->_rawData = array_merge($data,$this->_rawData);
        $data = $this->processData($data, $normalizeDataKeys, $applyDefaults, $castStringBooleans);

        $data = array_merge($this->_data, $data);
        $this->_data = $data;
        return $this;
    }

    /**
     * Sets the data for the class. The optional second
     * parameter, normalizeDataKeys, allows for all data
     * keys to be normalized into camelCase
     *
     * @param array $data The data
     * @param bool $normalizeDataKeys Flag used to normalize data keys
     * @param bool $applyDefaults Flag used to enable applying defaults
     * @return array
     */
    private function processData(array $data, $normalizeDataKeys = true, $applyDefaults = true, $castStringBooleans=true)
    {
        if ($applyDefaults) {
            $defaultData = $this->defineDataDefaults();
            if (!empty($defaultData)) {
                $this->log('Input Data | Apply Defaults Beginning', Logger::DEBUG);
                $data = $this->applyDefaults($data, $defaultData);
                $this->log('Input Data | Apply Defaults Completed', Logger::DEBUG);
            }else{
                $this->log('Input Data | Apply Defaults Skipped, No Defaults Defined', Logger::DEBUG);
            }
        }

        foreach ($this->dataProcessors as $dataProcessor) {
            $data = $dataProcessor->process($data);
        }

        if ($castStringBooleans) {
            $this->log('Input Data | Cast String Booleans Beginning', Logger::DEBUG);
            $data = $this->castStringBooleans($data);
            $this->log('Input Data | Cast String Booleans Completed', Logger::DEBUG);
        }

        if ($normalizeDataKeys) {
            $this->log('Input Data | Normalization Beginning', Logger::DEBUG);
            $data = $this->normalizeDataKeys($data);
            $this->log('Input Data | Normalization Completed', Logger::DEBUG);
        }

        return $data;
    }

    /**
     * Recursively apply default values to an array of input
     * data based on key. NULL and missing values in the input array
     * are replaced by the values in the defaults array.
     *
     * @param array $input The input data to apply defaults to
     * @param array $default An array of defaults
     *
     * @return array The resulting array of input data with defaults applied
     */
    protected function applyDefaults(array $input, array $default = [])
    {
        $this->flattenArray($default);

        $propertyAccessor = PropertyAccess::createPropertyAccessor();
        foreach ($default as $key => $defaultValue) {
            $inputValue = $propertyAccessor->getValue($input, $key);
            if (empty($inputValue) && !empty($default)) {
                $propertyAccessor->setValue($input, $key, $defaultValue);
            }
        }

        return $input;
    }

    /**
     * Casting strings of "true" and "false" to booleans
     *
     * @param mixed $data An array of data
     *
     * @return array The resulting array with string booleans cast to boolean
     */
    public function castStringBooleans($data)
    {
        if (!is_array($data)) {
            if (is_string($data) && in_array(strtolower($data), ['true', 'false'])) {
                if (strtolower($data) === 'true') {
                    $data = true;
                }
                if (strtolower($data) === 'false') {
                    $data = false;
                }
            }
            return $data;
        }

        $result = [];
        foreach ($data as $key=>$value) {
            $result[$key] = $this->castStringBooleans($value);
        }
        return $result;
    }

    /**
     * Normalize input data keys into camelCase
     *
     * @param mixed $data An array of data
     *
     * @return array The resulting array with keys normalized into camelCase
     */
    public function normalizeDataKeys($data)
    {
        if ($data instanceof NormalizationExemptData) {
            return $data->getData();
        }
        if (!is_array($data)) {
            return $data;
        }
        $result = [];
        foreach ($data as $key=>$value) {
            $key = $this->normalizeString($key);
            $result[$key] = $this->normalizeDataKeys($value);
        }
        return $result;
    }

    /**
     * Denormalize input data keys into snake_case
     *
     * @param mixed $data An array of data
     *
     * @param string $delimiter
     * @return array The resulting array with keys denormalized into snake_case
     */
    public function denormalizeDataKeys($data, $delimiter='_')
    {
        if ($data instanceof NormalizationExemptData) {
            return $data->getData();
        }

        if (!is_array($data)) {
            return $data;
        }

        $result = [];
        foreach ($data as $key=>$value) {
            // snake_case key
            $intermediateKey = preg_replace('/([^A-Z]{1,})([A-Z]{2,})/', '\\1'.$delimiter.'\\2', $key);
            $intermediateKey = preg_replace('/([A-Z])([^A-Z]{1,}|\z)/', $delimiter.'\\0', $intermediateKey);

            $key = strtolower($intermediateKey);

            $result[$key] = $this->denormalizeDataKeys($value);
        }
        return $result;
    }

    /**
     * Flatten an array. The result is an array with property access propertyPaths as the keys
     *
     * @param array $elements The elements to flatten
     * @param array|null $subNode The sub-node to flatten
     * @param null $path The current path in the array hierarchy
     */
    final protected function flattenArray(array &$elements, array $subNode = null, $path = null)
    {
        if (null === $subNode) {
            $subNode = &$elements;
        }

        foreach ($subNode as $key => $value) {
            if (is_array($value)) {
                $nodePath = $path ? $path.'['.$key.']' : '['.$key.']';
                $this->flattenArray($elements, $value, $nodePath);
                if (null === $path) {
                    unset($elements[$key]);
                }
            } elseif (null !== $path) {
                $elements[$path.'['.$key.']'] = $value;
            }else{
                unset($elements[$key]);
                $elements['['.$key.']'] = $value;
            }
        }
    }

    /**
     * Provides access to a data item through a key
     *
     * @param string $key The key of the data item to retrieve
     *
     * @param null $haystack
     * @return mixed The value of the data item
     */
    private function dataAccessor($key, $haystack=null)
    {
        if (null === $haystack) {
            $haystack = $this->_data;
        }
        // simple array key access to values
        if (array_key_exists($key, $haystack)) {
            return $haystack[$key];
        }

        // dot notation access to values
        if (!empty($key) && substr_count($key,'.')) {
            $value = array_reduce(explode('.', $key), function ($values, $param) {

                if (is_array($values) && array_key_exists($param, $values)) {
                    return $values[$param];
                }
                return '___VALUENOTFOUND___';

            }, $haystack);
            if ($value !== '___VALUENOTFOUND___') {
                return $value;
            }
        }

        // property path access to values
        if (!empty($key) && substr_count($key,'[')) {
            $propertyAccessor = PropertyAccess::createPropertyAccessorBuilder()
                ->enableExceptionOnInvalidIndex()
                ->getPropertyAccessor();
            if ($propertyAccessor->isReadable($haystack, $key)) {
                return $propertyAccessor->getValue($haystack, $key);
            }
        }

        throw new InvalidArgumentException(sprintf('Data Key "%s" not found', $key));

    }

    /**
     * @param $string
     * @return string|string[]
     */
    protected function normalizeString($string)
    {
        $groupFirstSet = ctype_upper(substr($string, 0, 2));
        // camelize key
        $string = str_replace([' ', '_', '-', '\\', '/'], '', ucwords($string, ' _-/\\'));

        if ((!ctype_upper($string) && !ctype_upper(substr($string, 1, 1))) || !$groupFirstSet) {
            $string = lcfirst($string);
        }
        return $string;
    }

    public function addDataProcessor(DataProcessorInterface $dataProcessor)
    {
        $this->dataProcessors[] = $dataProcessor;
    }

    final public static function getDataDefaults()
    {
        $refl = new ReflectionClass(static::class);
        $refl->getMethod('defineDataDefaults')->setAccessible(true);
        /** @var Collection $constraints */
        return ($refl->newInstanceWithoutConstructor())->defineDataDefaults();
    }

    public function offsetExists($offset)
    {
        return array_key_exists($offset, $this->_data);
    }

    public function offsetGet($offset)
    {
        return $this->dataAccessor($offset);
    }

    public function offsetSet($offset, $value)
    {
        $this->_data[$offset] = $value;
    }

    public function offsetUnset($offset)
    {
        unset($this->_data[$offset]);
    }
}
