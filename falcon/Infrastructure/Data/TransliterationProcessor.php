<?php

namespace Clubster\Falcon\Infrastructure\Data;

class TransliterationProcessor implements DataProcessorInterface
{
    public function process($data)
    {
        if (!is_array($data)) {
            if (is_string($data)) {
                $data = html_entity_decode(htmlspecialchars_decode($data));
                return transliterator_transliterate("Hex-Any/Java",$data);
            }
            return $data;
        }

        $result = [];
        foreach ($data as $key=>$value) {
            $result[$key] = $this->process($value);
        }
        return $result;
    }
}
