<?php

namespace Clubster\Falcon\Infrastructure\Data;

use Clubster\Falcon\Infrastructure\Logger\LoggerAware;

class DataObject implements DataAwareInterface
{
    use DataAware;
    use LoggerAware;

    private function __construct(array $data = [])
    {
        $this->setData($data);
    }

    public static function create(array $data = []): DataObject
    {
        return new static($data);
    }
}
