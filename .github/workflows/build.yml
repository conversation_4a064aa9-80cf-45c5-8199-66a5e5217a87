name: Build and Push To Amazon Elastic Container Registry
on:
  push:
    branches:
      - main

env:
  AWS_REGION: us-east-2
  IMAGE_TAG: ${{ github.ref == 'refs/heads/main' && 'bluejay' || github.ref_name }}
  BLUEJAY_IMAGE_TAG: 'bluejay'
  S3_BUCKET: ${{ secrets.S3_BUCKET }}
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
  DATE: $(date +%Y.%m.%d)

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Source service.sh and set environment variables
        run: |
          source deployment/service.sh
          echo "ECR_REPOSITORY=$SERVICE_ECR_REPO" >> $GITHUB_ENV
          echo "SERVICE_NAME=$SERVICE_NAME" >> $GITHUB_ENV

      - uses: act10ns/slack@v2
        with:
          status: starting
          channel: '#oliver-dev'
          message: ${{env.SERVICE_NAME}} Starting Docker Build and Push...
        if: always()

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download Sequence File from S3
        uses: prewk/s3-cp-action@v2
        with:
          source: s3://${{ env.S3_BUCKET }}/${{ env.SERVICE_NAME }}-${{ env.DATE }}.json
          dest: ${{ env.SERVICE_NAME }}-${{ env.DATE }}.json
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ${{ env.AWS_REGION }}
        continue-on-error: true

      - name: Generate Image Tag
        id: generate-tag
        run: |
          DATE=$(date +%Y.%m.%d)
          TIME=$(date +%H:%M:%S)
          SEQ_FILE="${{ env.SERVICE_NAME }}-${DATE}.json"
          BUILD_SHA=${{ github.sha }}
          if [ ! -f "$SEQ_FILE" ]; then
            echo "[]" > $SEQ_FILE
          fi
          SEQ=$(jq '.[-1].seq // 0' $SEQ_FILE)
          SEQ=$(printf "%03d" $((SEQ + 1)))
          NEW_ENTRY=$(jq -n --arg date "$DATE" --arg time "$TIME" --arg seq "$SEQ" --arg sha "$BUILD_SHA" '{date: $date, time: $time, seq: ($seq | tonumber), sha: $sha}')
          jq '. += [$NEW_ENTRY]' --argjson NEW_ENTRY "$NEW_ENTRY" $SEQ_FILE > tmp.$$.json && mv tmp.$$.json $SEQ_FILE
          IMAGE_TAG="$DATE.$SEQ"
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV

      - name: Upload Sequence File to S3
        uses: prewk/s3-cp-action@v2
        with:
          source: ${{ env.SERVICE_NAME }}-${{ env.DATE }}.json
          dest: s3://${{ env.S3_BUCKET }}/${{ env.SERVICE_NAME }}-${{ env.DATE }}.json
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws_region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ env.ECR_REPOSITORY }}
          IMAGE_TAG: ${{ env.IMAGE_TAG }}
          LATEST_IMAGE_TAG: ${{ env.LATEST_IMAGE_TAG }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:$BLUEJAY_IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$BLUEJAY_IMAGE_TAG

      - uses: act10ns/slack@v2
        with:
          status: ${{ job.status }}
          steps: ${{ toJson(steps) }}
          channel: '#oliver-dev'
        if: always()
