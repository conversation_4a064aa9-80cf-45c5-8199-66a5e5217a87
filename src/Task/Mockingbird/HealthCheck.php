<?php
namespace Mockingbird\Task\Mockingbird;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Listener\Http\RoutableTaskInterface;
use Mockingbird\Mockingbird;
use Symfony\Component\Validator\Constraints;

class HealthCheck extends Task implements RoutableTaskInterface
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $services = [
                'database' => []
            ];

            if (!$this->connection->isConnected()) {
                $this->connection->connect();
                $services['database']['reconnection_attempted']= true;
            }

            $services['database']['is_connected'] = $this->connection->isConnected();

            $listeners = Mockingbird::Listeners();
            $runningListeners = [];
            foreach ($listeners as $name=>$listener) {
                $runningListeners[] = [
                    'name'=>$name,
                    'uri'=>$listener->getUri(),
                    'handled_requests' => $listener->getHandledRequestCount()
                ];
            }

            $response = [
                'status'=>'ok',
                'boot_time' => Mockingbird::$bootTime,
                'uptime'=> (new Carbon(Mockingbird::$bootTime))->diffForHumans(null, CarbonInterface::DIFF_ABSOLUTE, true, 6),
                'listeners' => $runningListeners,
                'services'=> $services
            ];

            $this->log(sprintf('<info>health check %s</info>', $response));

            return $response;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [

            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    public function getRoutableName(): string
    {
        return 'health-check';
    }
}
