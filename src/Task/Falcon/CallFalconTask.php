<?php
namespace Clubster\Peregrine\Task\Falcon;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\MultipartStream;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'falcon.task.call')]
#[ActivityInterface(prefix: 'falcon.task.call')]
class CallFalconTask extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $tempOutputStructure = null;

            $outputStructure = $this->getData('outputStructure',[]);
            $task = $this->getData('task',null);
            $data = $this->getData('data',[]);

            $client = new Client([
                'base_uri' => $_ENV['FALCON_URL'],
                RequestOptions::VERIFY => false,
                RequestOptions::TIMEOUT => 60,
                RequestOptions::CONNECT_TIMEOUT => 60
            ]);

            $payload = self::prepPayload($data, 'payload');
            if (!empty($outputStructure)) {
                $outputStructure = self::prepPayload($outputStructure, 'outputStructure');
                $payload = array_merge($payload, $outputStructure);
            }
            array_unshift($payload,[
                'name'=>'task',
                'contents'=>$task
            ]);

            $body = new MultipartStream($payload);

//            $options[RequestOptions::MULTIPART] = $payload;
            $falconRequest = new Request('POST', $_ENV['FALCON_URL']);
            $falconRequest = $falconRequest->withBody($body);
            $falconRequest = $falconRequest->withHeader('Content-Type', 'multipart/form-data; boundary='.$body->getBoundary());
            $falconRequest = $falconRequest->withHeader('x-falcon-task', $task);
            if ($this->hasData('headers')) {
                foreach ($this->getRawData('headers') as $key => $value) {
                    $falconRequest = $falconRequest->withHeader($key, $value);
                }
            }

            $resp = $client->send($falconRequest);

            $headers = $resp->getHeaders();

            $resp = (string)$resp->getBody()->getContents();
            if (!empty($resp)) {
                $resp = json_decode($resp, true);
            }

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Request processed successfully')
                ->withData($resp);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }

    private static function  prepPayload($data, $key=null, &$out=[])
    {
        foreach ($data as $fieldName=>$value) {
            $fieldName = empty($key)?$fieldName:$key.'['.$fieldName.']';
            if (is_array($value)) {
                $out = array_merge($out, self::prepPayload($value, $fieldName));
                continue;
            }
            if ($value instanceof \SplFileInfo) {
                $out[] = [
                    'name'=>$fieldName,
                    'contents'=>fopen($value->getRealPath(),'r')
                ];
                continue;
            }
            $out[] = [
                'name'=>$fieldName,
                'contents'=>$value
            ];
        }

        return $out;
    }
}
