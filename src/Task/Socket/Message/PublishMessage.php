<?php
namespace Clubster\Peregrine\Task\Socket\Message;

use Clubster\Falcon\Infrastructure\Request\Request;
use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'socket.message.publish')]
#[ActivityInterface(prefix: 'socket.message.publish')]
class PublishMessage extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $data = $this->getData('data', []);
            $channel = $this->getData('channel', []);

            $client = new Client([
                'base_uri' => $_ENV['SOCKET_URL']
            ]);

            $client->post('/api/publish',[
                RequestOptions::HEADERS=> [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'apikey ' . $_ENV['SOCKET_API_KEY'],
                ],
                RequestOptions::JSON => [
                    'channel' => $channel,
                    'data' => $data,
                ],
            ]);

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Message published successfully')
                ->withData([]);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
