<?php
namespace Clubster\Peregrine\Task\Settings;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'settings.load')]
#[ActivityInterface(prefix: 'settings.load')]
class LoadSetting extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            // Check if the slug is provided
            if (!$this->getData('slug', false)) {
                throw new Exception('The "slug" parameter is required to load the organization setting value.');
            }
            // work here
            $settingQuery = $this->database()
                ->select()
                ->from('entity_settings.settings')
                ->where('slug', '=', $this->getData('slug'));

            $settings = $settingQuery->fetchAll();

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Setting loaded successfully')
                ->withData($settings);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
