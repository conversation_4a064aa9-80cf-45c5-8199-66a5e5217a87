<?php
namespace Clubster\Peregrine\Task\Post\Media;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Enum\Post\Media\DynamicImageLayout;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Cache\CacheAware;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use GdImage;
use Psr\Http\Message\ServerRequestInterface;
use Safe\Exceptions\ImageException;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'post.media.compose-dynamic-image')]
#[ActivityInterface(prefix: 'post.media.compose-dynamic-image')]
class ComposeDynamicPostImage extends AbstractTask
{
    use ActivityHandler;
    use CacheAware;

    const string VIDEO_PLAY_BUTTON = '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';

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        ini_set('memory_limit', '-1');
        try {

            $columns = $this->getData('columns',[]);
            $orientation = $this->getData('orientation',DynamicImageLayout::HORIZONTAL);
            $additionalCount = $this->getData('additionalCount',0);
            $postId = $this->getData('postId', false);
            if (!empty($postId) && empty($columns)) {
                $layout = $this->get(DetermineLayout::class)
                    ->setData(['postId' => $postId])();

                if ($layout->hasData('columns')) {
                    $columns = $layout->getData('columns');
                    $orientation = $layout->getData('orientation',DynamicImageLayout::HORIZONTAL);
                    $additionalCount = $layout->getData('additionalCount',0);
                } else {
                    throw new Exception('No media files found for post id: ' . $postId);
                }
            }

            $outputWidth = 872;
            $outputHeight = 480;

            $columnImages = [];
            foreach ($columns as $columnNum=>$column) {
                foreach ($column as $mediaNum=>$media) {
                    if (!empty($media['image'])) {
                        Logger::debug('media image: '.$media['image']);
                        try {
                            $image = \Safe\imagecreatefromstring(file_get_contents($media['image']));
                            $image = $this->flattenImage($image);
                            $columnImages[$columnNum][$mediaNum] = $this->normalizeImageOrientation($image, $media['image']);
                            \Safe\imagedestroy($image);
                        }catch (ImageException $e) {
                            Logger::error('Error creating image from string with url '.json_encode($media['image']).': '.$e->getMessage());

                        }
                    }
                }
            }

            if ($this->hasData('forceOrientation')) {
                $orientation = $this->getData('forceOrientation');
            }

            $isSingleImage = count($columns[0]) === 1 && empty($columns[1]);

            if ($isSingleImage) {
                $img = $columnImages[0][0];
                $w = \Safe\imagesx($img);
                $h = \Safe\imagesy($img);
                $ratio = $outputWidth/$w;
                $outputHeight = $h * $ratio;

            }

            $image = \Safe\imagecreatetruecolor($outputWidth, $outputHeight);
            $backgroundColor = imagecolorallocate($image, 255, 255, 255);
            \Safe\imagefill($image, 0, 0, $backgroundColor);


            $orientation = DynamicImageLayout::from($orientation);
            Logger::debug('Orientation: '.$orientation->value);
            if ($orientation == DynamicImageLayout::HORIZONTAL) {

                $column1MediaWidth = $outputWidth - 3 - 3;
                $column1MediaHeight = $outputHeight - 3 - 3;
                $column2MediaWidth = 0;
                $column2MediaHeight = 0;

                if (count($columns[0]) === 2) {
                    $column1MediaWidth = 431.5;
                }
                if (!empty($columns[1])) {
                    $column1MediaHeight = 310;
                    $column2MediaHeight = 161;
                    if (count($columns[1]) === 2) {
                        $column2MediaWidth = 431.5;
                    }
                    if (count($columns[1]) === 3) {
                        $column2MediaWidth = 286.6;
                    }
                }
                $padding = 3;
                $column1Pos = 0;
                foreach ($columns[0] as $mediaNum => $media) {
                    $mediaResampling = $this->determineImageCropBox($columnImages[0][$mediaNum], $column1MediaWidth, $column1MediaHeight, !$isSingleImage);
                    $columns[0][$mediaNum]['resample'] = $mediaResampling;
                    \Safe\imagecopyresampled(
                        dst_image: $image,
                        src_image: $columnImages[0][$mediaNum],
                        dst_x: $padding + $column1Pos,
                        dst_y: $padding,
                        src_x: $mediaResampling['cropX'],
                        src_y: $mediaResampling['cropY'],
                        dst_width: $mediaResampling['width'],
                        dst_height: $mediaResampling['height'],
                        src_width: $mediaResampling['srcWidth'],
                        src_height: $mediaResampling['srcHeight'],
                    );

                    // Add play button if it's a video
                    if ($media['discr'] === 'video' || $media['discr'] === 'youtubevideo') {
                        $playButton = \Safe\imagecreatefromstring(base64_decode(self::VIDEO_PLAY_BUTTON));
                        $this->addPlayButton($image, $playButton, $padding + $column1Pos, $padding, $mediaResampling['width'], $mediaResampling['height']);
                    }

                    $column1Pos += $column1MediaWidth + $padding;
                }

                if (!empty($columns[1] ?? [])) {
                    $column2Pos = 0;
                    foreach ($columns[1] as $mediaNum => $media) {
                        $mediaResampling = $this->determineImageCropBox($columnImages[1][$mediaNum], $column2MediaWidth, $column2MediaHeight, !$isSingleImage);
                        $columns[1][$mediaNum]['resample'] = $mediaResampling;
                        \Safe\imagecopyresampled(
                            dst_image: $image,
                            src_image: $columnImages[1][$mediaNum],
                            dst_x: $padding + $column2Pos,
                            dst_y: $padding + $column1MediaHeight + $padding,
                            src_x: $mediaResampling['cropX'],
                            src_y: $mediaResampling['cropY'],
                            dst_width: $mediaResampling['width'],
                            dst_height: $mediaResampling['height'],
                            src_width: $mediaResampling['srcWidth'],
                            src_height: $mediaResampling['srcHeight'],
                        );
                        if ($media['discr'] === 'video' || $media['discr'] === 'youtubevideo') {
                            $playButton = \Safe\imagecreatefromstring(base64_decode(self::VIDEO_PLAY_BUTTON));
                            $this->addPlayButton($image, $playButton, $padding + $column2Pos, $padding + $column1MediaHeight + $padding, $mediaResampling['width'], $mediaResampling['height']);
                        }
                        $column2Pos += $column2MediaWidth + $padding;
                    }
                }
            } else {
                $column1MediaWidth = $outputWidth - 3 - 3;
                $column1MediaHeight = $outputHeight - 3 - 3;
                $column2MediaWidth = 0;
                $column2MediaHeight = 0;

                if (count($columns[0]) === 2) {
                    $column1MediaHeight = 235.5;
                }
                if (!empty($columns[1])) {
                    $column1MediaWidth = 570;
                    $column2MediaWidth = 291;
                    if (count($columns[1]) === 2) {
                        $column2MediaHeight = 235.5;
                    }
                    if (count($columns[1]) === 3) {
                        $column2MediaHeight = 156;
                    }
                }

                $padding = 3;
                $column1Pos = 0;
                foreach ($columns[0] as $mediaNum => $media) {
                    $mediaResampling = $this->determineImageCropBox($columnImages[0][$mediaNum], $column1MediaWidth, $column1MediaHeight);
                    $columns[0][$mediaNum]['resample'] = $mediaResampling;
                    \Safe\imagecopyresampled(
                        dst_image: $image,
                        src_image: $columnImages[0][$mediaNum],
                        dst_x: $padding,
                        dst_y: $padding + $column1Pos,
                        src_x: $mediaResampling['cropX'],
                        src_y: $mediaResampling['cropY'],
                        dst_width: $mediaResampling['width'],
                        dst_height: $mediaResampling['height'],
                        src_width: $mediaResampling['srcWidth'],
                        src_height: $mediaResampling['srcHeight'],
                    );
                    if ($media['discr'] === 'video' || $media['discr'] === 'youtubevideo') {
                        $playButton = \Safe\imagecreatefromstring(base64_decode(self::VIDEO_PLAY_BUTTON));
                        $this->addPlayButton($image, $playButton, $padding, $padding + $column1Pos, $mediaResampling['width'], $mediaResampling['height']);
                    }
                    $column1Pos += $column1MediaHeight + $padding;
                }

                if (!empty($columns[1] ?? [])) {
                    $column2Pos = 0;
                    foreach ($columns[1] as $mediaNum => $media) {
                        $mediaResampling = $this->determineImageCropBox($columnImages[1][$mediaNum], $column2MediaWidth, $column2MediaHeight);
                        $columns[1][$mediaNum]['resample'] = $mediaResampling;
                        \Safe\imagecopyresampled(
                            dst_image: $image,
                            src_image: $columnImages[1][$mediaNum],
                            dst_x: $padding + $column1MediaWidth + $padding,
                            dst_y: $padding + $column2Pos,
                            src_x: $mediaResampling['cropX'],
                            src_y: $mediaResampling['cropY'],
                            dst_width: $mediaResampling['width'],
                            dst_height: $mediaResampling['height'],
                            src_width: $mediaResampling['srcWidth'],
                            src_height: $mediaResampling['srcHeight'],
                        );
                        if ($media['discr'] === 'video' || $media['discr'] === 'youtubevideo') {
                            $playButton = \Safe\imagecreatefromstring(base64_decode(self::VIDEO_PLAY_BUTTON));
                            $this->addPlayButton($image, $playButton, $padding + $column1MediaWidth + $padding, $padding + $column2Pos, $mediaResampling['width'], $mediaResampling['height']);
                        }
                        $column2Pos += $column2MediaHeight + $padding;
                    }
                }
            }


            \Safe\ob_start();
            \Safe\imagejpeg($image, null, 0100);
            \Safe\imagedestroy($image);
            $buffer = ob_get_contents();
            $length = ob_get_length();
            \Safe\ob_end_clean();

            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Dynamic post image generated successfully')
                ->withData([
                    'postId' => $postId,
                    'image' => base64_encode($buffer),
                    'length' => $length,
                ]);

        } catch (Exception $exception) {
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }

    private function determineImageCropBox($src, $width, $height, $crop = true)
    {
        $img = $src;
        $w = \Safe\imagesx($img);
        $h = \Safe\imagesy($img);

        $ratio = max($width/$w, $height/$h);


        if ($width > $height) {
            $x=0;
            $y = ($h - $height / $ratio) / 2;
        } else {
            $x = ($w - $width / $ratio) / 2;
            $y = 0;
        }

        if (!$crop) {
            $x = 0;
            $y = 0;
        }

        $h = $height / $ratio;
        $w = $width / $ratio;

        return [
            'cropX' => $x,
            'cropY' => $y,
            'width' => $width,
            'height' => $height,
            'srcWidth' => $w,
            'srcHeight' => $h
        ];
    }

    /**
     * Adds a play button overlay to the center of an image area
     */
    private function addPlayButton(GdImage $destImage, GdImage $playButton, int $x, int $y, int $width, int $height): void
    {
        // Get play button dimensions
        $buttonWidth = \Safe\imagesx($playButton);
        $buttonHeight = \Safe\imagesy($playButton);

        // Calculate center position
        $centerX = $x + ($width - $buttonWidth) / 2;
        $centerY = $y + ($height - $buttonHeight) / 2;

        // Copy the play button with alpha transparency
        \Safe\imagecopy(
            $destImage,
            $playButton,
            (int)$centerX,
            (int)$centerY,
            0,
            0,
            $buttonWidth,
            $buttonHeight
        );
    }

    private function flattenImage(GdImage $image): GdImage
    {
        $width = \Safe\imagesx($image);
        $height = \Safe\imagesy($image);

        $backgroundColor = $this->sampleImageColors($image, $width, $height);

        $destImage = imagecreatetruecolor($width, $height);

        $backgroundColor = ($backgroundColor === 'white')
            ? imagecolorallocate($destImage, 255, 255, 255)
            : imagecolorallocate($destImage, 0, 0, 0);

        \Safe\imagefill($destImage, 0, 0, $backgroundColor);
        \Safe\imagealphablending($destImage, true);
        \Safe\imagesavealpha($destImage, false);
        \Safe\imagecopy($destImage, $image, 0, 0, 0, 0, $width, $height);
        \Safe\imagedestroy($image);
        return $destImage;
    }

    private function sampleImageColors(GDImage $image, $width, $height): string
    {
        $hasTransparency = false;
        $brightnessSum = 0;
        $sampleCount = 0;

        for ($y = 0; $y < $height; $y += 2) {
            for ($x = 0; $x < $width; $x += 2) {
                $colorIndex = imagecolorat($image, $x, $y);
                $rgba = imagecolorsforindex($image, $colorIndex);

                // Check for full transparency
                if (isset($rgba['alpha']) && $rgba['alpha'] === 127) {
                    $hasTransparency = true;
                    continue;
                }

                // Skip fully transparent, include visible or opaque
                $r = $rgba['red'];
                $g = $rgba['green'];
                $b = $rgba['blue'];

                // Luminance calculation
                $brightness = (0.299 * $r + 0.587 * $g + 0.114 * $b);
                $brightnessSum += $brightness;
                $sampleCount++;
            }
        }

        if ($sampleCount === 0 || !$hasTransparency) {
            return 'white';
        }

        $averageBrightness = $brightnessSum / $sampleCount;

        if ($averageBrightness > 128) {
            return 'black';
        } else {
            return 'white';
        }
    }

    function normalizeImageOrientation(GdImage $image, string $imagePath): GdImage {
        if (!function_exists('exif_read_data')) {
            return $image; // EXIF not available
        }

        $exif = @exif_read_data($imagePath);
        if (!$exif || !isset($exif['Orientation'])) {
            return $image;
        }

        $orientation = $exif['Orientation'];

        switch ($orientation) {
            case 2: // Mirror horizontal
                imageflip($image, IMG_FLIP_HORIZONTAL);
                break;
            case 3: // Rotate 180
                $image = imagerotate($image, 180, 0);
                break;
            case 4: // Mirror vertical
                imageflip($image, IMG_FLIP_VERTICAL);
                break;
            case 5: // Mirror horizontal and rotate 270 CW
                imageflip($image, IMG_FLIP_HORIZONTAL);
                $image = imagerotate($image, 270, 0);
                break;
            case 6: // Rotate 90 CW
                $image = imagerotate($image, -90, 0);
                break;
            case 7: // Mirror horizontal and rotate 90 CW
                imageflip($image, IMG_FLIP_HORIZONTAL);
                $image = imagerotate($image, -90, 0);
                break;
            case 8: // Rotate 270 CW
                $image = imagerotate($image, -270, 0);
                break;
        }

        return $image;
    }
}
