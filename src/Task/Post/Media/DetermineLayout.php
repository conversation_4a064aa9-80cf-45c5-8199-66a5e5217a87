<?php
namespace Clubster\Peregrine\Task\Post\Media;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Enum\Post\Media\DynamicImageLayout;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Clubster\Peregrine\Task\Post\LoadPost;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'post.media.determine-layout')]
#[ActivityInterface(prefix: 'post.media.determine-image')]
class DetermineLayout extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        ini_set('memory_limit', '-1');
        try {
            $mediaFiles = $this->getData('mediaFiles', []);
            $postId = $this->getData('postId', false);
            if (!empty($postId) && empty($mediaFiles)) {
                $mediaFileResponse = $this->get(LoadPostMedia::class)
                                    ->setData(['postId' => $postId])();
                if ($mediaFileResponse->hasError()) {
                    throw new Exception('Error loading media files: ' . json_encode($mediaFileResponse->getError()));
                }
                $mediaFiles = $mediaFileResponse->getData();

                if (!empty($mediaFiles[$postId])) {
                    $mediaFiles = $mediaFiles[$postId];
                } else {
                    throw new Exception('No media files found for post id: ' . $postId);
                }
            }

            if (empty($mediaFiles)) {
                throw new Exception('No media files specified');
            }

            if (!array_is_list($mediaFiles)) {
                $mediaFiles = [$mediaFiles];
            }

            // sort media by ordinal
            usort($mediaFiles, function ($a, $b) {
                return $a['ordinal'] <=> $b['ordinal'];
            });

            $totalFileCount = count($mediaFiles);
            $displayFileCount = min(5, $totalFileCount);
            $additionalCount = $totalFileCount - $displayFileCount;

            $firstFile = reset($mediaFiles);
            if ($firstFile['discr'] === 'video' && !empty($firstFile['poster'])) {
                $firstFile = $firstFile['poster'];
            }
            $orientation = $this->determineImageOrientation($firstFile);

            $col1Count = $displayFileCount;

            if ($displayFileCount > 2) {
                $col1Count = ($displayFileCount + $additionalCount <= 4) ? 1 : 2;
            }

            if ($displayFileCount === 2) {
                $col1Count = 2;
                if ($orientation === DynamicImageLayout::VERTICAL) {
                    $orientation = DynamicImageLayout::HORIZONTAL;
                }else{
                    $orientation = DynamicImageLayout::VERTICAL;
                }
            }

            $col2Count = $displayFileCount-$col1Count;
            $columns= [
                0=>array_splice($mediaFiles, 0, $col1Count),
                1=>array_splice($mediaFiles, 0, $col2Count)
            ];

            $layoutColumns = array_filter($columns);

            $layout = [
                'columns' => $layoutColumns,
                'orientation' => $orientation,
                'displayFileCount' => $displayFileCount,
                'totalFileCount' => $totalFileCount,
                'additionalCount' => max($additionalCount,0),
                'additionalMedia' => $mediaFiles
            ];

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Dynamic Image Layout Generated')
                ->withData($layout);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }

    private function determineImageOrientation($file): DynamicImageLayout
    {
        if (!empty($file['width']) && !empty($file['height'])) {
            $width = $file['width'];
            $height = $file['height'];

        }

        [$width, $height] = getimagesize($file['image']);

        if ($width >= $height) {
            return DynamicImageLayout::HORIZONTAL;
        }

        return DynamicImageLayout::VERTICAL;
    }
}
