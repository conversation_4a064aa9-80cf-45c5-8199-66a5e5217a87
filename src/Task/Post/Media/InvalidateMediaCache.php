<?php
namespace Clubster\Peregrine\Task\Post\Media;

use Aws\CloudFront\CloudFrontClient;
use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'post.media.invalidate-cache')]
#[ActivityInterface(prefix: 'post.media.invalidate-cache')]
class InvalidateMediaCache extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            // invalidate cache from CloudFront
            $cloudFront = $this->get(CloudFrontClient::class);
            $distributionId = $this->getData('distributionId', null);
            $paths = $this->getData('paths', []);
            if (empty($distributionId) || empty($paths)) {
                throw new Exception('Distribution ID and paths are required to invalidate cache');
            }
            if (!is_array($paths)) {
                $paths = [$paths];
            }
            $paths = array_map(function ($path) {
                return '/' . ltrim($path, '/');
            }, $paths);
            $paths = array_unique($paths);
            $paths = array_filter($paths, function ($path) {
                return !empty($path);
            });
            if (empty($paths)) {
                throw new Exception('No valid paths provided for cache invalidation');
            }
            $result = $cloudFront->createInvalidation([
                'DistributionId' => $distributionId,
                'InvalidationBatch' => [
                    'Paths' => [
                        'Quantity' => count($paths),
                        'Items' => $paths,
                    ],
                    'CallerReference' => uniqid('invalidate-', true),
                ],
            ]);
            if ($result['@metadata']['statusCode'] !== 201) {
                throw new Exception('Failed to create invalidation: ' . json_encode($result));
            }

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Cache invalidation request submitted successfully')
                ->withData(json_decode(json_encode($result, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR));

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
