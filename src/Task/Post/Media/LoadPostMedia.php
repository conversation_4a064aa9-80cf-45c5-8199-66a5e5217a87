<?php
namespace Clubster\Peregrine\Task\Post\Media;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Cycle\Database\Injection\Fragment;
use Cycle\Database\Query\SelectQuery;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'post.media.load')]
#[ActivityInterface(prefix: 'post.media.load')]
class LoadPostMedia extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        ini_set('memory_limit', '-1');
        try {
            $postId = $this->getData('postId', []);
            if (!is_array($postId)) {
                $postId = [$postId];
            }
            if (empty($postId)) {
                throw new Exception('No post id specified');
            }

            // Create a query builder to fetch media data
            $mediaSql = $this->database()
                             ->select([])
                             ->from('public.files as files')
                            ->innerJoin('posts.media as post_media')->on('post_media.file_id','=','files.id')
                            ->leftJoin('public.videos as videos')->on('videos.id','=','files.id')
                            ->leftJoin('public.images as images')->on('images.id','=','files.id')
                            ->where('post_media.post_id','IN',$postId);

            if (!$this->getData('includeDeleted', false)) {
                $mediaSql->where(static function (SelectQuery $query) {
                    $query->where('post_media.deleted_at', 'IS', new Fragment('NULL'))
                        ->orWhere('post_media.deleted_at', '>', new Fragment('NOW()'));
                });
            }

            $media = $mediaSql->fetchAll();

            // Merge video poster data into the media array
            foreach ($media as $key => $item) {
                switch ($item['_discr']) {
                    case 'video':
                        $uriParts = explode('.',$item['uri']);
                        array_pop($uriParts);
                        $uriParts[] = '0000001';
                        $uriParts[] = 'jpg';
                        $image = implode('.', $uriParts);
                        $headers = get_headers($image);
                        if (!stripos($headers[0],"200 OK")) {
                            $uriParts = explode('.',$media['uri']);
                            array_pop($uriParts);
                            $uriParts[] = 'png';
                            $image = implode('.', $uriParts);
                        }

                        $media[$key]['image'] = $image;
                        break;
                    case 'youtubevideo':
                        preg_match('/^.*(youtu.be\/|v\/|u\/\w\/|shorts\/|embed\/|watch\?v=|\&v=|\?v=)([^#\&\?]*).*/',$item['uri'], $matches);
                        $youtubeVideoId = $matches[2];
                        if (empty($youtubeVideoId)) {
                            continue 2;
                        }
                        $media[$key]['status'] = 'published';
                        $media[$key]['uri']=sprintf('https://www.youtube.com/embed/%s?autoplay=0',$youtubeVideoId);
                        $media[$key]['image']=sprintf('https://img.youtube.com/vi/%s/0.jpg',$youtubeVideoId);
                        break;
                    default:
                        $media[$key]['image'] = $item['uri'];
                        break;
                }
            }

            // group media by post id
            $media = array_reduce($media, function ($carry, $item) {
                $carry[$item['post_id']][] = $item;
                return $carry;
            }, []);

            // sort media by ordinal
            foreach ($media as $postId => $items) {
                usort($items, function ($a, $b) {
                    return $a['ordinal'] <=> $b['ordinal'];
                });
                $media[$postId] = $items;
            }

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Post Media Loaded')
                ->withData($media);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
