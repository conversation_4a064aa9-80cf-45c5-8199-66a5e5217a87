<?php
namespace Clubster\Peregrine\Task\Post;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Cache\CacheAware;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'post.load')]
#[ActivityInterface(prefix: 'post.load')]
class LoadPost extends AbstractTask
{
    use ActivityHandler;
    use CacheAware;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $postId = $this->getData('postId', []);
            if (!is_array($postId)) {
                $postId = [$postId];
            }
            if (empty($postId)) {
                throw new Exception('No post id specified');
            }

            $posts = $this->supplementWithCache($postId, function ($postId) {
                $post = $this->database()->select([])
                    ->from('public.posts')
                    ->where('id', '=', $postId)
                    ->limit(1)
                    ->fetchAll();

                if (empty($post)) {
                    return [];
                }

                return current($post);
            });

//
//            $userId = $this->getData('userId', null);

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Post loaded successfully')
                ->withData($posts ?? []);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
