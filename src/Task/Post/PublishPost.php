<?php
namespace Clubster\Peregrine\Task\Post;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Clubster\Peregrine\Task\Falcon\CallFalconTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'post.publish')]
#[ActivityInterface(prefix: 'post.publish')]
class PublishPost extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $postId = $this->getData('postId', false);
            if (empty($postId)) {
                throw new Exception('Post ID is required');
            }

            $falconTaskResponse = $this->get(CallFalconTask::class)
                                ->setData([
                                    'task' => 'Post:Utility:PublishPost',
                                    'data' => [
                                        'id' => $postId,
                                    ]
                                ])()
                                ->getData();

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Post published successfully')
                ->withData($falconTaskResponse);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
