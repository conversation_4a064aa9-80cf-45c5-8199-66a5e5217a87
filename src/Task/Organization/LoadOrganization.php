<?php
namespace Clubster\Peregrine\Task\Organization;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Cycle\Database\Injection\Parameter;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.load')]
#[ActivityInterface(prefix: 'organization.load')]
class LoadOrganization extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {

            $select = $this->database()
                ->select()
                ->from('organizations.organizations');

            if ($this->getData('organizationId', false)) {
                $id = $this->getData('organizationId');
                if (!is_array($id)) {
                    $id = [$id];
                }
                $select->where('id', 'IN', new Parameter($id))
                    ->limit(count($id));

            } elseif ($this->getData('slug', false)) {
                $slug = $this->getData('slug');
                if (!is_array($slug)) {
                    $slug = [$slug];
                }
                $select->where('slug', 'IN', new Parameter($slug))
                    ->limit(count($slug));

            } else {
                throw new \Exception('Either "id" or "slug" must be provided to load an organization.');
            }

            $organizations = $select->fetchAll();

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Organizations loaded successfully')
                ->withData(['organizations' => $organizations]);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
