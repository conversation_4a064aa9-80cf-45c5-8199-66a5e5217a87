<?php
namespace Clubster\Peregrine\Task\Organization\Settings;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Cycle\Database\Injection\Parameter;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.settings.value.load')]
#[ActivityInterface(prefix: 'organization.settings.value.load')]
class LoadOrganizationSettingValue extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            // Check if the setting id is provided
            if (!$this->getData('settingId', false) && !$this->getData('organizationId', false)) {
                throw new \Exception('The "settingId" or "organizationId" parameter is required to load the organization setting value.');
            }

            // Fetch the setting value from the database
            $settingValueQuery = $this->database()
                ->select([
                    'setting.title AS setting_title',
                    'setting.default_value AS setting_default',
                    'setting.slug AS setting_slug',
                    'organization_setting_value.organization_id',
                    'values.value AS setting_value',
                    'values.id AS setting_value_id',
                    'setting_options.label AS setting_option_label',
                    'setting_options.value AS setting_option_value',
                ])
                ->from('entity_settings.values')
                ->join('INNER', 'entity_settings.settings', 'setting', ['setting.id','=','values.setting_id'])
                ->join('INNER', 'organization_setting_value', 'organization_setting_value', ['organization_setting_value.setting_value_id','=','values.id'])
                ->join('LEFT', 'entity_settings.options', 'setting_options', ['values.option_id','=','setting_options.id']);

            // check if the setting id is provided
            if ($this->getData('settingId', false)) {
                $settingId = $this->getData('settingId');
                if (!is_array($settingId)) {
                    $settingId = [$settingId];
                }
                $settingValueQuery->where('values.setting_id', 'IN', new Parameter($settingId));
            }

            // check if the organization id is provided
            if ($this->getData('organizationId', false)) {
                $organizationId = $this->getData('organizationId');
                if (!is_array($organizationId)) {
                    $organizationId = [$organizationId];
                }
                $settingValueQuery->where('organization_setting_value.organization_id', 'IN', new Parameter($organizationId));
            }

            // Execute the query and fetch all results
            $settingValues = $settingValueQuery->run()->fetchAll();

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Organization setting values loaded successfully')
                ->withData($settingValues);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
