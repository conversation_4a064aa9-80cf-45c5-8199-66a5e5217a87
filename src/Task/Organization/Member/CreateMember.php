<?php

namespace Clubster\Peregrine\Task\Organization\Member;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use DateTime;
use Psr\Http\Message\ServerRequestInterface;
use Ramsey\Uuid\Uuid;
use RuntimeException;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.member.create')]
#[ActivityInterface(prefix: 'organization.member.create')]
class CreateMember extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        if (empty($this->getData('userId',null))) {
            throw new RuntimeException('User ID is required to create a member');
        }

        if (empty($this->getData('organizationId',null))) {
            throw new RuntimeException('Organization ID is required to create a member');
        }

        if (empty($this->getData('createdBy',null))) {
            $this->mergeData(['createdBy'=>'81dc57d2-2bfa-4672-bc27-330e5c5560fc']);
        }

        $memberResponse = $this->get(LoadMember::class)([
            'organizationId' => $this->getData('organizationId'),
            'userId' => $this->getData('userId')
        ]);

        if (!empty($memberResponse) && $memberResponse->getStatusCode() === 200) {
            return $memberResponse;
        }

        $memberId = (string)Uuid::uuid4();

        $fields = [
            'id' => $memberId,
            'created_by_id' => $this->getData('createdBy'),
            'updated_by_id' => $this->getData('createdBy'),
            'organization_id' => $this->getData('organizationId'),
            'user_id' => $this->getData('userId'),
            'created_at' => new DateTime(),
            'updated_at' => new DateTime(),
            'allow_email'=>true,
            'allow_push'=>true,
            'member_number'=>$this->getData('memberNumber', null),
            'join_date'=>$this->getData('joinDate', null),
            'notes'=>$this->getData('notes', null),
            'status' => 'active',
            'suspended'=>false
        ];

        $this->database()->table('organizations.members')->insertOne($fields);

        $memberResponse = $this->get(LoadMember::class)(['memberId' => $memberId]);

        return $memberResponse;
    }
}
