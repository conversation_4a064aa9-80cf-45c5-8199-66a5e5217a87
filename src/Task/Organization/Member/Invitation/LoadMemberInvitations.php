<?php

namespace Clubster\Peregrine\Task\Organization\Member\Invitation;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Cycle\Database\Injection\Fragment;
use Cycle\Database\Injection\Parameter;
use Cycle\Database\Query\SelectQuery;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.member.invitation.load')]
#[ActivityInterface(prefix: 'organization.member.invitation.load')]
class LoadMemberInvitations extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        $selectQuery = $this->database()
            ->select()
            ->from('organizations.member_invitations');

        $statusFilter = [];
        if ($this->getData('filterOutDeclined', false)) {
            $statusFilter[] = 'declined';
        }

        if ($this->getData('filterOutExpired', false)) {
            $statusFilter[] = 'expired';
        }

        if ($this->getData('filterOutPending', false)) {
            $statusFilter[] = 'pending';
        }

        if ($this->getData('filterOutAccepted', false)) {
            $statusFilter[] = 'accepted';
        }

        if (!empty($statusFilter)) {
            $selectQuery->where('status', 'NOT IN', new Parameter($statusFilter));
        }

        if (!($this->getData('includeDeleted', false))) {
            $selectQuery->where(static function (SelectQuery $query) {
                $query->where('deleted_at', 'IS', new Fragment('NULL'))
                    ->orWhere('deleted_at', '>', new Fragment('NOW()'));
            });
        }

        if ($this->getData('organizationId', false)) {
            $organizationId = $this->getData('organizationId');
            if (!is_array($organizationId)) {
                $organizationId = [$organizationId];
            }
            $selectQuery->where('organization_id', 'IN', new Parameter($organizationId));
        }

        if ($this->getData('memberId', false)) {
            $memberId = $this->getData('memberId');
            if (!is_array($memberId)) {
                $memberId = [$memberId];
            }
            $selectQuery->where('member_id', 'IN', new Parameter($memberId));
        }

        $invitations = $selectQuery->fetchAll();

        if (empty($invitations)) {
            return (new Response(
                404,
                ['Content-Type' => 'application/json']
            ))->withMessage('No invitations found');
        }

        return (new Response(
            200,
            ['Content-Type' => 'application/json']
        ))->withMessage('Member invitations loaded successfully')
          ->withData($invitations);
    }
}
