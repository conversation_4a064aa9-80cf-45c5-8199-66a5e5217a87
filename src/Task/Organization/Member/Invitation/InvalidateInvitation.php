<?php

namespace Clubster\Peregrine\Task\Organization\Member\Invitation;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Cycle\Database\Injection\Parameter;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.member.invitation.invalidate')]
#[ActivityInterface(prefix: 'organization.member.invitation.invalidate')]
class InvalidateInvitation extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        $invitations = $this->getData('invitationId', false);
        if (empty($invitations)) {
            return (new Response(
                400,
                ['Content-Type' => 'application/json']
            ))->withMessage('No invitations provided to invalidate');
        }
        if (!is_array($invitations)) {
            $invitations = [$invitations];
        }

        $invitationUpdate = $this->database()
            ->table('organizations.member_invitations')
            ->update([
                'status'=>'expired',
                'updated_at'=>date('Y-m-d H:i:s'),
                'deleted_at'=>date('Y-m-d H:i:s'),
                'updated_by_id'=>$this->getData('createdById','81dc57d2-2bfa-4672-bc27-330e5c5560fc'),
            ])
            ->where('id', 'IN', new Parameter($invitations))
            ->run();

        if ($invitationUpdate === 0) {
            return (new Response(
                404,
                ['Content-Type' => 'application/json']
            ))->withMessage('No invitations found to invalidate');
        }

        return (new Response(
            200,
            ['Content-Type' => 'application/json']
        ))->withMessage('Invitations invalidated successfully')
          ->withData(['updatedCount' => $invitationUpdate]);
    }
}
