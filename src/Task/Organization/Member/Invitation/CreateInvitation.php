<?php

namespace Clubster\Peregrine\Task\Organization\Member\Invitation;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Psr\Http\Message\ServerRequestInterface;
use Ramsey\Uuid\Uuid;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.member.invitation.create')]
#[ActivityInterface(prefix: 'organization.member.invitation.create')]
class CreateInvitation extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        $member = $this->getData('memberId', false);
        if (empty($member)) {
            return (new Response(
                400,
                ['Content-Type' => 'application/json']
            ))->withMessage('Member data is required to create an invitation');
        }

        $organization = $this->getData('organizationId', false);
        if (empty($organization)) {
            return (new Response(
                400,
                ['Content-Type' => 'application/json']
            ))->withMessage('Organization data is required to create an invitation');
        }

        $invitationId = $this->database()->table('organizations.member_invitations')
            ->insertOne([
                'id' => (string)Uuid::uuid4(),
                'organization_id' => $organization,
                'member_id' => $member,
                'created_by_id'=>$this->getData('createdById','81dc57d2-2bfa-4672-bc27-330e5c5560fc'),
                'updated_by_id'=>$this->getData('createdById','81dc57d2-2bfa-4672-bc27-330e5c5560fc'),
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);

        if (empty($invitationId)) {
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))->withMessage('Failed to create invitation');
        }

        return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))->withMessage('Invitation created successfully')
              ->withData(['invitationId' => $invitationId]);

    }
}
