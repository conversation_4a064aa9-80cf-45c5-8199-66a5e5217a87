<?php
namespace Clubster\Peregrine\Task\Organization\Member;

use Aws\S3\S3Client;
use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.member.load-bulk-file')]
#[ActivityInterface(prefix: 'organization.member.load-bulk-file')]
class LoadBulkMemberFile extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $s3 = $this->container->get(S3Client::class);

            $memberDataFile = $this->getData('memberDataFile');
            $columnDefinitions = $this->getRawData('columnDefinitions');
            $columnDefinitions = array_flip($columnDefinitions);

            $tempKey = tempnam(sys_get_temp_dir(), 'bulk-process-');
            $s3->getObject([
                'Bucket'=>$_ENV['S3_DATA_EXCHANGE_BUCKET'],
                'Key'=>$memberDataFile,
                'SaveAs'=>$tempKey
            ]);
            $file = new \SplFileObject($tempKey);
            $file->setFlags(\SplFileObject::READ_CSV);
            $members = [];
            while(!$file->eof()) {
                $line = $file->fgetcsv();
                $line = array_combine($columnDefinitions, $line);
                // clean data?
                $output = [
                    'email' => filter_var($line['email'], FILTER_SANITIZE_EMAIL)
                ];
                unset($line['email']);

                $line = array_map(fn($item) => htmlspecialchars($item), $line);
                $output = array_merge($output, $line);
                $members[] = $output;
            }

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Member Bulk File Loaded')
                ->withData($members);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
