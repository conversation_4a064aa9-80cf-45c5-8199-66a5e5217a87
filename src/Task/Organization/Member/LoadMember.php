<?php

namespace Clubster\Peregrine\Task\Organization\Member;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Clubster\Peregrine\Task\User\LoadUser;
use Cycle\Database\Injection\Parameter;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.member.loadMember')]
#[ActivityInterface(prefix: 'organization.member.loadMember')]
class LoadMember extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        $query = $this->database()->select()
            ->from('organizations.members');

        // check by organization and user id also
        if ($this->getData('organizationId', false) && $this->getData('userId', false)) {
            $query->where('organization_id', '=', $this->getData('organizationId'));
            $query->where('user_id', '=', $this->getData('userId'));
        }

        if ($this->getData('memberId', false)) {
            $memberId = $this->getData('memberId');
            if (!is_array($memberId)) {
                $memberId = [$memberId];
            }
            $query->where('id', 'in', new Parameter($memberId));
        }

        $members = $query->fetchAll();

        $loadUser = $this->get(LoadUser::class);
        $members = array_map(function ($member) use ($loadUser) {
            $member['user'] = $loadUser->setData(['userId' => $member['user_id']])()->getData()[0]??[];
            return $member;
        }, $members);

        if (empty($members)) {
            return (new Response(
                404,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Member not found');
        }

        return (new Response(
            200,
            ['Content-Type' => 'application/json']
        ))
            ->withMessage('Member loaded successfully')
            ->withData($members);
    }
}
