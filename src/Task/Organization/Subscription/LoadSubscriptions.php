<?php
namespace Clubster\Peregrine\Task\Organization\Subscription;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Clubster\Peregrine\Task\User\LoadUser;
use Clubster\Peregrine\Task\User\Notification\LoadPushTokens;
use Cycle\Database\Injection\Fragment;
use Cycle\Database\Query\SelectQuery;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'organization.member.subscription.load')]
#[ActivityInterface(prefix: 'organization.member.subscription.load')]
class LoadSubscriptions extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $organizationId = $this->getData('organizationId', false);
            $userId = $this->getData('userId', false);
            $requireSubscription = $this->getData('requireSubscription',true);

            if (empty($organizationId) || empty($userId)) {
                throw new Exception('Organization ID and User ID are required to load subscriptions');
            }

            if (!is_array($organizationId)) {
                $organizationId = [$organizationId];
            }
            if (!is_array($userId)) {
                $userId = [$userId];
            }

            if (!$requireSubscription && count($organizationId) > 1) {
                throw new Exception('Multiple organization IDs are not allowed when requiring subscriptions');
            }

            // Fetch subscriptions from the database
            $subscriptionQuery = $this->database()
                ->select()
                ->where(static function (SelectQuery $query) {
                    $query->where('deleted_at', 'IS', new Fragment('NULL'))
                        ->orWhere('deleted_at', '>', new Fragment('NOW()'));
                })
                ->andWhere('status','!=','declined')
                ->from('organizations.subscriptions');

            if (!empty($organizationId)) {
                $subscriptionQuery->where('organization_id', 'IN', $organizationId);
            }
            if (!empty($userId)) {
                $subscriptionQuery->where('user_id', 'IN', $userId);
            }
            $subscriptionData = $subscriptionQuery->run()->fetchAll();

            if (empty($subscriptionData)) {
                if ($requireSubscription) {
                    throw new Exception('No subscriptions found for the provided organization and user IDs');
                }
            }

            $pushTokens = $this->get(LoadPushTokens::class)
                                ->setData([
                                  'userId' => $userId
                                ])();
            $pushTokens = $pushTokens->getData();

            $userPushTokenMap = [];
            foreach ($pushTokens as $pushToken) {
                if (!in_array($pushToken['userId'], $userPushTokenMap)) {
                    $userPushTokenMap[$pushToken['userId']] = [];
                }
                $userPushTokenMap[$pushToken['userId']][] = $pushToken['token'];
            }

            $userResponse = $this->get(LoadUser::class)?->setData(['userId' => $userId])();
            $users = $userResponse->getdata() ?? [];
            $userMap = [];
            foreach ($users as $user) {
                $userMap[$user['id']] = $user;
            }

            // if enforceSubscription is false, ensure all users have a subscription
            if (!$requireSubscription) {
                $usersWithSubscriptions = array_column($subscriptionData, 'user_id');
                $usersWithoutSubscriptions = array_diff($userId, $usersWithSubscriptions);
                foreach ($usersWithoutSubscriptions as $userIdWithoutSubscription) {
                    $subscriptionData[] = [
                        'id' => null,
                        'user_id' => $userIdWithoutSubscription,
                        'organization_id' => $organizationId[0],
                        'status' => 'active',
                        'allow_push' => false,
                        'allow_feed' => false,
                        'allow_email' => true,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                        'bounce_count' => 0
                    ];
                }
            }

            $subscriptionData = array_map(function ($subscription) use ($userPushTokenMap, $userMap) {
                return [
                    'id' => $subscription['id'],
                    'user_id' => $subscription['user_id'],
                    'organization_id' => $subscription['organization_id'],
                    'status' => $subscription['status'],
                    'allowPush' => $subscription['allow_push']??false,
                    'allowFeed' => $subscription['allow_feed']??false,
                    'allowEmail' => $subscription['allow_email']&&($subscription['status'] !== 'undeliverable')??false,
                    'allow' => [
                        'push' => $subscription['allow_push'] ?? false,
                        'feed' => $subscription['allow_feed'] ?? false,
                        'email' => $subscription['allow_email'] && ($subscription['status'] !== 'undeliverable') ?? false
                    ],
                    'created_at' => $subscription['created_at'],
                    'updated_at' => $subscription['updated_at'],
                    'user'=> $userMap[$subscription['user_id']]?? null,
                    'pushTokens' => $subscription['allow_push'] ? $userPushTokenMap[$subscription['user_id']]??[] : [],
                    'bounceCount' => $subscription['bounce_count']
                ];
            }, $subscriptionData);

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Subscriptions loaded successfully')
                ->withData($subscriptionData);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
