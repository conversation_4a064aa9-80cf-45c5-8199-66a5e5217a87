<?php
namespace Clubs<PERSON>\Peregrine\Task\Filesystem;

use Aws\S3\S3Client;
use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Enum\Filesystem\S3Acl;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'filesystem.s3.put')]
#[ActivityInterface(prefix: 'filesystem.s3.put')]
class PutFileToS3 extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $s3Client = $this->get(S3Client::class);
            $bucket = $this->getData('bucket');
            if (empty($bucket)) {
                throw new Exception('Bucket name is required');
            }

            $filePath = $this->getData('filePath', null);
            if (!empty($filePath)) {
                // read the file into a stream
                $fileStream = fopen($filePath, 'rb');
            }

            if (empty($fileStream)) {
                $fileContent = $this->getData('fileContent', null);
                $base64Content = $this->getData('base64Content', false);
                if ($base64Content) {
                    $fileContent = base64_decode($base64Content);
                }
                if (!empty($fileContent)) {
                    // create a stream from the content
                    $fileStream = fopen('php://temp', 'rb+');
                    fwrite($fileStream, $fileContent);
                    rewind($fileStream);
                } else {
                    throw new Exception('Either filePath or fileContent must be provided');
                }
            }

            $key = $this->getData('key', basename($filePath));
            if (empty($key)) {
                throw new Exception('Key is required for the S3 object');
            }
            if (!is_string($key)) {
                throw new Exception('Key must be a string');
            }
            // put stream to s3
             $result = $s3Client->putObject([
                'Bucket' => $bucket,
                'Key' => strtolower($key),
                'Body' => $fileStream,
                'ContentType' => $this->getData('contentType', 'application/octet-stream'),
                'CacheControl' => $this->getData('cacheControl', 'max-age=31536000'),
                'Metadata' => array_filter(array_merge($this->getData('metadata', []),[
                    'env' => $_ENV['API_ENV']??'cardinal',
                    'filename' => basename($this->getData('filename', $key)),
                    'user' => $request->getHeader('x-u')?? null,
                    'input_bucket' => $this->getData('inputBucket', null),
                    'output_bucket' => $this->getData('outputBucket', null),
                ])),
            ]);

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('File uploaded successfully to S3')
                ->withData(json_decode(json_encode($result, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR));

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
