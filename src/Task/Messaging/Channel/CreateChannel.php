<?php

namespace Mockingbird\Task\Messaging\Channel;

use Exception;
use <PERSON>ckingbird\Infrastructure\Task\Task;
use Ramsey\Uuid\Uuid;
use Symfony\Component\Validator\Constraints;

class CreateChannel extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $id = (string)Uuid::uuid4();
            $createSql = $this->connection->createQueryBuilder();
            $createSql->insert('messaging.channels')
                ->setValue('id', $createSql->createNamedParameter($id))
                ->setValue('name', $createSql->createNamedParameter($this->getData('name', null)))
                ->setValue('type', $createSql->createNamedParameter($this->getData('type')))
                ->setValue('status', $createSql->createNamedParameter('active'))
                ->setValue('created_by_id', $createSql->createNamedParameter($this->getData('userId')))
                ->setValue('updated_by_id', $createSql->createNamedParameter($this->getData('userId')))
                ->setValue('created_at', 'NOW()')
                ->setValue('updated_at', 'NOW()');

            $createSql->executeQuery();
            return $id;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'name'=>new Constraints\Optional(),
                'userId'=>new Constraints\Required(),
                'type'=>new Constraints\Required(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
