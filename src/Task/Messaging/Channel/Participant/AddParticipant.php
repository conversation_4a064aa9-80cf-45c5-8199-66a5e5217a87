<?php

namespace Mockingbird\Task\Messaging\Channel\Participant;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Symfony\Component\Validator\Constraints;

class AddParticipant extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $participantSql = $this->connection->createQueryBuilder();
            $participantSql->insert('messaging.participants')
                            ->setValue('channelId', $participantSql->createNamedParameter($this->getData('channelId')))
                            ->setValue('userId', $participantSql->createNamedParameter($this->getData('userId')))
                            ->executeQuery();

            return $this->getData();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'channelId'=>new Constraints\Required(),
                'userId'=>new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
