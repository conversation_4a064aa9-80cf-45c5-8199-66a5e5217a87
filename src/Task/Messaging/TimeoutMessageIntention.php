<?php

namespace Mockingbird\Task\Messaging;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Listener\Messenger\Channel\ChannelManager;
use Symfony\Component\Validator\Constraints;

class TimeoutMessageIntention extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $intentionId = $this->getData('intentionId');
            $channelId = $this->getData('channelId');
                ChannelManager::GetChannelById($channelId)
                    ->broadcast([
                        'type' => 'message:intention-timeout',
                        'data' => [
                            'intention' => [
                                'id' => $intentionId
                            ],
                        ]
                    ], $this->request->getConnection(), false)
                    ->removeDeferredAction('intention-' . $intentionId);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'intentionId'=>new Constraints\Required(),
                'channelId'=>new Constraints\Required()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
