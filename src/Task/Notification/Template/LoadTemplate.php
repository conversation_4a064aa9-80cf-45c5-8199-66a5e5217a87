<?php
namespace Clubster\Peregrine\Task\Notification\Template;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'notification.template.load')]
#[ActivityInterface(prefix: 'notification.template.load')]
class LoadTemplate extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            // load the template from the database or any other source
            $templateQuery = $this->database()->select()
                                ->from('notifications.templates');
            // Retrieve the template ID from the request data
            $templateSlug = $this->getData('templateSlug', false);
            if (!empty($templateSlug)) {
                $templateQuery->where('slug', '=', $templateSlug);
            }

            $templateId = $this->getData('templateId', false);
            if (!empty($templateId)) {
                $templateQuery->where('id', '=', $templateId);
            }

            if ($this->getData('deliveryMethod', false)) {
                $templateQuery->where('delivery_method', '=', $this->getData('deliveryMethod'));
            }

            if (empty($templateId) && empty($templateSlug)) {
                throw new Exception('Either templateId or templateSlug must be provided');
            }

            $templates = $templateQuery->fetchAll();

            if (empty($templates)) {
                // If no template is found, return an empty response
                return new Response(
                    404,
                    ['Content-Type' => 'application/json'],
                    json_encode(['message' => 'Template not found'], JSON_THROW_ON_ERROR)
                );
            }

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Templates loaded successfully')
                ->withData($templates);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
