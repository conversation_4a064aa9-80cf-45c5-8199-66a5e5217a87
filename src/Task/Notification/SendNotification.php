<?php
namespace Clubster\Peregrine\Task\Notification;

use Carbon\Carbon;
use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Clubster\Peregrine\Task\Notification\Recipient\GenerateRecipient;
use Clubster\Peregrine\Task\Notification\Template\LoadTemplate;
use Clubster\Peregrine\Task\Organization\LoadOrganization;
use Exception;
use MongoDB\Client;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'notification.send')]
#[ActivityInterface(prefix: 'notification.send')]
class SendNotification extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            // gather recipients
            $recipients = $this->getData('recipients', false);
            if (empty($recipients)) {
                throw new Exception('Recipients are required');
            }
            if (!is_array($recipients)) {
                $recipients = [$recipients];
            }

            $senderOrganization = $this->getData('senderOrganization', null);
            $sender = $this->getData('sender', null);
            if ($senderOrganization === null && $sender === null) {
                throw new Exception('Either senderOrganization or sender must be provided');
            }

            $recipients = $this->get(GenerateRecipient::class)
                                ->setData([
                                    'recipients' => $recipients,
                                    'senderOrganization' => $this->getData('senderOrganization', null),
                                    'sender' => $this->getData('sender', null),
                                    'requireSubscription' => $this->getData('requireSubscription', true),
                                ])()
                                ->getData();

            // gather templates
            $templateSlug = $this->getData('templateSlug', false);
            if (empty($templateSlug)) {
                throw new Exception('Template slug is required');
            }
            $template = $this->get(LoadTemplate::class)
                            ->setData([
                                'templateSlug'=>$templateSlug
                            ])()
                            ->getData();
            if (empty($template)) {
                throw new Exception('Template not found for the provided slug');
            }

            $deliveryMethods = array_column($template, 'deliveryMethod');
            $templates = array_combine($deliveryMethods, $template);

            // generate template data
            $templateData = $this->getSupplementedTemplateData();
            $forcedDeliveryMethods = $this->getData('forcedDeliveryMethods', []);
            $disabledDeliveryMethods = $this->getData('disabledDeliveryMethods', []);

            if ($forcedDeliveryMethods !== null && !is_array($forcedDeliveryMethods)) {
                $forcedDeliveryMethods = [$forcedDeliveryMethods];
            }
            if ($disabledDeliveryMethods !== null && !is_array($disabledDeliveryMethods)) {
                $disabledDeliveryMethods = [$disabledDeliveryMethods];
            }

            // map recipients to notifications
            $notifications = [];
            foreach ($recipients as $recipient) {
                $allowedMethods = [];
                if (!in_array('internal', $disabledDeliveryMethods)) {
                    $allowedMethods = ['internal'];
                }

                if (($recipient['allowEmail'] || in_array('email', $forcedDeliveryMethods, true)) && !empty($recipient['user']['email']) && !in_array('email', $disabledDeliveryMethods)) {
                    $allowedMethods[$recipient['user']['email']] = 'email';
                }

                if (($recipient['allowPush'] || in_array('push', $forcedDeliveryMethods, true)) && !empty($recipient['pushTokens']) && !in_array('push', $disabledDeliveryMethods)) {
                    foreach ($recipient['pushTokens'] as $pushToken) {
                        $allowedMethods[$pushToken] = 'push';
                    }
                }

                foreach ($allowedMethods as $methodRecipient => $method) {
                    if (empty($templates[$method])) {
                        continue;
                    }
                    $notifications[] = $this->get(GenerateNotification::class)
                        ->setData([
                            'recipient_id' => $recipient['userId'],
                            'recipient' => $methodRecipient,
                            'template' => $templates[$method]['id'],
                            'send_at' => $this->getData('sendAt', Carbon::now()->toDateString()),
                            'delivery_method' => $method,
                            'template_data' => $templateData,
                            'sender' => $this->getData('sender', null),
                            'sender_organization' => $this->getData('senderOrganization', null),
                            'reply_to' => $this->getData('replyTo', null),
                        ])()
                        ->getRawData();
                }
            }

            $notifications = array_filter($notifications);
            $documents = array_map(function($notificationData) {
                return [
                    'notification_data' => $notificationData,
                    'type'=> $this->getData('discriminator', 'notification'),
                    'execute_time' => time(),
                    'queue' => null,
                    'seen'=>false,
                    'seen_at' => null
                ];
            }, $notifications);
            $documents = array_values($documents);

            if (!empty($documents)) {
                $mongo = $this->get(Client::class);
                $mongo->selectDatabase($_ENV['MONGO_DB_NAME'])
                    ->selectCollection('notification_queue')
                    ->insertMany($documents, ['ordered'=>false]);
            }

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('')
                ->withData([]);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }

    private function getSupplementedTemplateData()
    {
        $templateData = $this->getData('templateData', []);
        if ($this->getData('senderOrganization', false)) {
            $organizationData = $this->get(LoadOrganization::class)
                                    ->setData([
                                        'organizationId' => $this->getData('senderOrganization')
                                    ])()
                                    ->getData('organizations',[]);
            if (!empty($organizationData)) {
                $organization = [
                    'id' => $organizationData[0]['id'],
                    'name' => $organizationData[0]['name'],
                    'slug' => $organizationData[0]['slug'],
                    'avatar' => [
                        'image' => [
                            'uri' => $organizationData[0]['avatar']['image']['uri'] ?? null
                        ]
                    ],
                    'cover' => [
                        'image' => [
                            'uri' => $organizationData[0]['cover']['image']['uri'] ?? null
                        ]
                    ],
                    'url'=>$_ENV['WWW_URL'].'organization/'.$organizationData[0]['slug'].'/'
                ];

                if (!empty($organizationData[0]['parent']['name'])) {
                    $organizationParent = [
                        'id' => $organizationData[0]['parent']['id'],
                        'name' => $organizationData[0]['parent']['name'],
                        'slug' => $organizationData[0]['parent']['slug'],
                        'avatar' => [
                            'image' => [
                                'uri' => $organizationData[0]['parent']['avatar']['image']['uri'] ?? null
                            ]
                        ],
                        'cover' => [
                            'image' => [
                                'uri' => $organizationData[0]['parent']['cover']['image']['uri'] ?? null
                            ]
                        ],
                        'url'=>$_ENV['WWW_URL'].'organization/'.$organizationData[0]['parent']['slug'].'/'
                    ];
                    $organization['parent'] = $organizationParent;
                }

                $templateData['organization'] = $organization;
                $templateData['logoImage'] = $organizationData[0]['avatar']['image']['uri']??'https://media.clubster.com/clubster_logo.png';
                $templateData['logoLink'] = $_ENV['WWW_URL'].'organization/'.$organizationData[0]['slug'].'/';
                $templateData['coverImage'] = $organizationData[0]['cover']['image']['uri']??'https://media.clubster.com/mute_geo_bg.jpg';
                $templateData['metadata']['sender'] = $organizationData[0]['name'];
                $templateData['metadata']['sender_id'] = $this->getData('sender', null);
                $templateData['metadata']['sender_organization_id'] = $organizationData[0]['id'];
            }
        }
        return $templateData;
    }
}
