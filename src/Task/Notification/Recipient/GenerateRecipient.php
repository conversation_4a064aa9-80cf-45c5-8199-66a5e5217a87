<?php
namespace Clubster\Peregrine\Task\Notification\Recipient;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Clubster\Peregrine\Task\Organization\Subscription\LoadSubscriptions;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'notification.recipient.generate')]
#[ActivityInterface(prefix: 'notification.recipient.generate')]
class GenerateRecipient extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $recipients = $this->getData('recipients', false);
            if (empty($recipients)) {
                throw new Exception('Recipients are required');
            }
            if (!is_array($recipients)) {
                $recipients = [$recipients];
            }

            if ($this->hasData('senderOrganization')) {
                // this is a organization notification
                $senderOrganization = $this->getData('senderOrganization', null);
                if (empty($senderOrganization)) {
                    throw new Exception('Sender organization is required');
                }
                $subscriptions = $this->container->get(LoadSubscriptions::class)->setData([
                    'userId' => $recipients,
                    'organizationId' => $senderOrganization,
                    'requireSubscription' => $this->getData('requireSubscription', true),
                ])()->getData();
            } elseif($this->hasData('sender')) {
                // this is a friend notification
                $sender = $this->getData('sender', null);
                if (empty($sender)) {
                    throw new Exception('Sender is required');
                }
                $subscriptions = $this->container->get(LoadSubscriptions::class)->setData([
                    'userId' => $recipients,
                    'friendId' => $sender,
                    'requireSubscription' => $this->getData('requireSubscription', true),
                ])()->getData();
            }

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Recipients generated successfully')
                ->withData($subscriptions);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
