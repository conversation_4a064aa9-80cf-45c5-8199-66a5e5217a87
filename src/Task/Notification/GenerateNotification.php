<?php
namespace Clubster\Peregrine\Task\Notification;

use Carbon\Carbon;
use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Enum\Notification\NotificationStatus;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Ramsey\Uuid\Uuid;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'notification.generate')]
#[ActivityInterface(prefix: 'notification.generate')]
class GenerateNotification extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $id = $this->getData('id', (string)Uuid::uuid4());

            $data = [
                'id'=>$id,
                'post_id'=>$this->getData('postId', null),
                'recipient_id'=>$this->getData('recipientId',null),
                'recipient'=> $this->getData('recipient', null),
                'template_id'=> $this->getData('template'),
                'status'=> NotificationStatus::PENDING,
                'send_at'=> $this->getData('sendAt', Carbon::now()->toDateTimeString()),
                'created_at'=> Carbon::now()->toDateTimeString(),
                'updated_at'=> Carbon::now()->toDateTimeString(),
                'delivery_method'=> $this->getData('deliveryMethod'),
                'template_data'=> $this->getData('templateData', []),
                'read'=> false,
                'sender'=> $this->getData('sender', null),
                'sender_organization'=> $this->getData('senderOrganization', null),
                'reply_to'=> $this->getData('replyTo', null),
                'discr'=> $this->getData('discriminator', 'notification'),
            ];

            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Notification generated successfully')
                ->withData($data);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
