<?php

namespace Mockingbird\Task\User\Friend;

use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Task\User\LoadUser;
use Mockingbird\ValueObjects\UserRelationship;
use Symfony\Component\Validator\Constraints;

class LoadFriendship extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $friendSql = $this->connection->createQueryBuilder();
            $friendSql->select([
                'friends.user_id',
                'friends.friend_id',
                'friends.id',
                'friends.status',
                'friends.created_at',
                'friends.updated_at',
                'friends.deleted_at',
                'friends.created_by_id',
                'subscription.allow_push',
                'subscription.allow_feed',
            ])

                ->from('users.friends','friends')
                ->leftJoin('friends', 'users.subscriptions', 'subscription', 'subscription.user_id = '.$friendSql->createPositionalParameter($this->getData('userId')).' AND subscription.friend_id = '.$friendSql->createPositionalParameter($this->getData('friendId')).' AND subscription.deleted_at IS NULL')

                ->where($friendSql->expr()->and(
                    $friendSql->expr()->eq('friends.user_id', $friendSql->createPositionalParameter($this->getData('userId'))),
                    $friendSql->expr()->eq('friends.friend_id', $friendSql->createPositionalParameter($this->getData('friendId'))),
                ))
                ->orWhere($friendSql->expr()->and(
                    $friendSql->expr()->eq('friends.user_id', $friendSql->createPositionalParameter($this->getData('friendId'))),
                    $friendSql->expr()->eq('friends.friend_id', $friendSql->createPositionalParameter($this->getData('userId'))),
                ))
                ->setMaxResults(1);

            if ($this->getData('allowDeleted', false) != true) {
                $friendSql->andWhere('friends.deleted_at IS NULL');
            }

            $friendship = $friendSql->execute()->fetchAssociative();

            if (empty($friendship)) {
                return null;
            }

            $friendPrefix = 'user';
            $userPrefix = 'friend';
            if ($friendship['user_id'] === $this->getData('userId')){
                $friendPrefix = 'friend';
                $userPrefix = 'user';
            }

            $loadUser = $this->container->get(LoadUser::class);
            return [
                'id' => $friendship['id'],
                'status' => $friendship['status'],
                'createdAt' => $friendship['created_at'],
                'updatedAt' => $friendship['updated_at'],
                'deletedAt' => $friendship['deleted_at'],

                'subscription'=>[
                    'allow_push'=>$friendship['allow_push'],
                    'allow_feed'=>$friendship['allow_feed'],
                ],

                'friend'=> $loadUser->setData(['userId'=>$friendship[$friendPrefix.'_id'], 'forceRelationship'=>UserRelationship::RELATIONSHIP_FRIEND_ACCEPTED])(),
                'user'=> $loadUser->setData(['userId'=>$friendship[$userPrefix.'_id'], 'forceRelationship'=>UserRelationship::RELATIONSHIP_FRIEND_ACCEPTED])(),
                'created_by'=> $loadUser->setData(['userId'=>$friendship['created_by_id'], 'forceRelationship'=>UserRelationship::RELATIONSHIP_FRIEND_ACCEPTED])(),
            ];

        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'userId' => new Constraints\Required(),
                'friendId' => new Constraints\Required(),
                'allowDeleted'=>new Constraints\Optional()
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }
}
