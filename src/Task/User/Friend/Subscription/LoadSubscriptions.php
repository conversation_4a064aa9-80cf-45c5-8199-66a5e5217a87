<?php

namespace Clubster\Peregrine\Task\User\Friend\Subscription;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Clubster\Peregrine\Task\User\LoadUser;
use Clubster\Peregrine\Task\User\Notification\LoadPushTokens;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'user.friend.subscriptions.load')]
#[ActivityInterface(prefix: 'user.subscriptions.load')]
class LoadSubscriptions extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $userId = $this->getData('userId', false);
            $friendId = $this->getData('friendId', false);
            $requireSubscription = $this->getData('requireSubscription',true);

            if (empty($userId) || empty($friendId)) {
                throw new \RuntimeException('User ID and Friend ID are required to load subscriptions');
            }

            if (!is_array($userId)) {
                $userId = [$userId];
            }
            if (!is_array($friendId)) {
                $friendId = [$friendId];
            }

            if (!$requireSubscription && count($friendId) > 1) {
                throw new \Exception('Multiple friend IDs are not allowed when requiring subscriptions');
            }

            // Fetch subscriptions from the database
            $subscriptionQuery = $this->database()
                ->select()
                ->from('user.friend_subscriptions')
                ->where([
                    'deleted_at' => [
                        'IS' => null,
                        '>', 'NOW()'
                    ]
                ])
                ->andWhere(['status', '!=', 'declined']);

            if (!empty($userId)) {
                $subscriptionQuery->where('user_id', 'IN', $userId);
            }
            if (!empty($friendId)) {
                $subscriptionQuery->where('friend_id', 'IN', $friendId);
            }

            $subscriptionData = $subscriptionQuery->run()->fetchAll();

            if (empty($subscriptionData)) {
                throw new \RuntimeException('No subscriptions found for the provided user and friend IDs');
            }

            $pushTokens = $this->get(LoadPushTokens::class)
                                ?->setData($request,['userId' => $userId])()
                                ?->getData();

            $userPushTokenMap = [];
            if ($pushTokens) {
                foreach($pushTokens as $pushToken) {
                    if (!isset($userPushTokenMap[$pushToken['user_id']])) {
                        $userPushTokenMap[$pushToken['user_id']] = [];
                    }
                    $userPushTokenMap[$pushToken['user_id']][] = $pushToken;
                }
            }

            $userResponse = $this->get(LoadUser::class)
                                ?->setData($request, ['userId' => $userId])()
                                ?->getData() ?? [];
            $userMap = [];
            foreach ($userResponse as $user) {
                $userMap[$user['id']] = $user;
            }

            // if enforceSubscription is false, ensure all users have a subscription
            if (!$requireSubscription) {
                $usersWithSubscriptions = array_column($subscriptionData, 'user_id');
                $usersWithoutSubscriptions = array_diff($userId, $usersWithSubscriptions);
                foreach ($usersWithoutSubscriptions as $userIdWithoutSubscription) {
                    $subscriptionData[] = [
                        'id' => null,
                        'user_id' => $userIdWithoutSubscription,
                        'friend_id' => $friendId[0],
                        'status' => 'active',
                        'allow_push' => false,
                        'allow_feed' => false,
                        'allow_email' => true,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                        'bounce_count' => 0
                    ];
                }
            }

            // Add user and push token information to the subscription data
            $subscriptionData = array_map(function ($subscription) use ($userPushTokenMap, $userMap) {
                return [
                    'id' => $subscription['id'],
                    'user_id' => $subscription['user_id'],
                    'friend_id' => $subscription['friend_id'],
                    'status' => $subscription['status'],
                    'allowPush' => $subscription['allow_push']??false,
                    'allowFeed' => $subscription['allow_feed']??false,
                    'allowEmail' => $subscription['allow_email']&&($subscription['status'] !== 'undeliverable')??false,
                    'allow' => [
                        'push' => $subscription['allow_push'] ?? false,
                        'feed' => $subscription['allow_feed'] ?? false,
                        'email' => $subscription['allow_email']&&($subscription['status'] !== 'undeliverable') ?? false
                    ],
                    'created_at' => $subscription['created_at'],
                    'updated_at' => $subscription['updated_at'],
                    'user' => $userMap[$subscription['user_id']] ?? null,
                    'push_tokens' => $subscription['allow_push'] ? $userPushTokenMap[$subscription['user_id']]??[] : []
                ];
            }, $subscriptionData);

            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))->withMessage('Subscription loaded successfully')
              ->withData($subscriptionData);

        } catch (\Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))->withMessage('An error occurred while loading the subscription')
              ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
