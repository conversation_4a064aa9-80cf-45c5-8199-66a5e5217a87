<?php

namespace Mockingbird\Task\User;

use C<PERSON>\Adapter\Common\CacheItem;
use C<PERSON>\Adapter\Predis\PredisCachePool;
use Doctrine\DBAL\Connection;
use Exception;
use Mockingbird\Infrastructure\Task\Task;
use Mockingbird\Task\Attributes\LoadAttributes;
use <PERSON>ckingbird\Task\Settings\LoadSettings;
use Mockingbird\ValueObjects\UserRelationship;
use Symfony\Component\Validator\Constraints;

class LoadUser extends Task
{
    /**
     * @throws Exception
     */
    protected function run()
    {
        try {
            $userIds = $this->getData('userId');
            if (!is_array($userIds)) {
                $userIds = [$userIds];
            }

            $cachedUsers = [];
            $uncachedUsers = $userIds;

            /** @var PredisCachePool $pool */
            $pool = $this->container->get(PredisCachePool::class);

            $cacheKeys = array_map(fn($userId) => 'user-'.$userId, $uncachedUsers);
            $cachedUsers = $pool->getItems($cacheKeys);
            $cachedUsers = array_filter($cachedUsers, fn($cachedUser) => $cachedUser->isHit());
            $cachedUsers = array_map(fn($cachedUser) => $cachedUser->get(), $cachedUsers);
            $cachedUsers = array_combine(array_column($cachedUsers, 'id'), $cachedUsers);

            $uncachedUsers = array_diff($uncachedUsers, array_keys($cachedUsers));

            if (!empty($uncachedUsers)) {
                $userSql = $this->connection->createQueryBuilder();
                $userSql->select([
                    'users.id',
                    'users.email',
                    'users.first_name',
                    'users.last_name',
                    'users.is_active',
                    'users.created_at',
                    'users.avatar_id as avatar_id',
                    'users.cover_id as cover_id',

                    'files.uri as avatar_uri',
                    'files_cover.uri as cover_uri',
                ])
                    ->from('users.users', 'users')
                    ->leftJoin('users', 'users.user_images', 'user_images', 'users.avatar_id = user_images."id" AND (user_images.deleted_at IS NULL OR user_images.deleted_at > (NOW() AT TIME ZONE \'utc\'))')
                    ->leftJoin('user_images', '"public".files', 'files', 'user_images.image_id = files."id" AND (files.deleted_at IS NULL OR files.deleted_at > (NOW() AT TIME ZONE \'utc\'))')
                    ->leftJoin('users', 'users.user_images', 'user_images_cover', 'users.cover_id = user_images_cover."id" AND (user_images_cover.deleted_at IS NULL OR user_images_cover.deleted_at > (NOW() AT TIME ZONE \'utc\'))')
                    ->leftJoin('user_images_cover', '"public".files', 'files_cover', 'user_images_cover.image_id = files_cover."id" AND (files_cover.deleted_at IS NULL OR files_cover.deleted_at > (NOW() AT TIME ZONE \'utc\'))')
                    ->where('users.id in (' . $userSql->createPositionalParameter($uncachedUsers, Connection::PARAM_STR_ARRAY) . ')');

                $users = $userSql->execute()->fetchAllAssociative();

                $userIds = array_column($users, 'id');
                $users = array_combine($userIds, $users);

                $users = array_map(static function ($user) {
                    return [
                        'id' => $user['id'],
                        'email' => $user['email'],
                        'is_active' => $user['is_active'],
                        'first_name' => $user['first_name'],
                        'last_name' => $user['last_name'],
                        'created_at' => $user['created_at'],
                        'avatar' => [
                            'image' => [
                                'uri' => $user['avatar_uri'],
                                'id' => $user['avatar_id'],
                            ],
                        ],
                        'cover' => [
                            'image' => [
                                'uri' => $user['cover_uri'],
                                'id' => $user['cover_id'],
                            ],
                        ],
                    ];
                }, $users);

                $includeAttributes = $this->getData('includeAttributes');
                $includeSettings = $this->getData('includeSettings');
                $calculateRelationshipWith = $this->getData('calculateRelationshipWith');

                $attributes = [];
                if ($includeAttributes) {
                    $attributes = $this->container->get(LoadAttributes::class)->setData([
                        'entityType' => 'user',
                        'entityId' => $userIds,
                        'retainEntityIdIndex' => true
                    ])();
                }

                $settings = $this->container->get(LoadSettings::class)->setData([
                    'entityType' => 'user',
                    'entityId' => $userIds,
                    'retainEntityIdIndex' => true
                ])();

                foreach ($users as $userId => $user) {
                    if (array_key_exists($userId, $attributes) && $includeAttributes) {
                        $users[$userId]['attributes'] = $attributes[$userId];
                    }

                    if (array_key_exists($userId, $settings) && $includeSettings) {
                        $users[$userId]['settings'] = $settings[$userId];
                    }

                    $relationship = UserRelationship::RELATIONSHIP_PUBLIC;

                    if ($this->getData('forceRelationship')) {
                        $relationship = $this->getData('forceRelationship');
                    } else {
                        if (!empty($calculateRelationshipWith)) {
                            $relationship = $this->container->get(DetermineUserRelationship::class)->setData([
                                'users' => [
                                    $userId,
                                    $calculateRelationshipWith
                                ]
                            ])();
                        }
                    }

                    switch ($relationship) {
                        default:
                        case UserRelationship::RELATIONSHIP_PUBLIC:
                            $nameSettings = $settings[$userId]['publicly-displayed-name']['value']['value'];
                            // this should be a setting?
                            $users[$userId]['email'] = '';
                            break;
                        case UserRelationship::RELATIONSHIP_FRIEND_PENDING:
                        case UserRelationship::RELATIONSHIP_FRIEND_ACCEPTED:
                            $nameSettings = $settings[$userId]['friends-displayed-name']['value']['value'];
                            break;
                        case UserRelationship::RELATIONSHIP_ORGANIZATION:
                        case UserRelationship::RELATIONSHIP_ORGANIZATION_STAFF:
                        case UserRelationship::RELATIONSHIP_ORGANIZATION_MEMBER:
                        case UserRelationship::RELATIONSHIP_SELF:
                            $nameSettings = 'FF-FL';
                            break;
                    }

                    $nameSettings = explode('-', $nameSettings);
                    $users[$userId]['privacyRelationship'] = $relationship;
                    if ($nameSettings[0] === 'NONE') {
                        $users[$userId]['first_name'] = '';
                        $users[$userId]['last_name'] = '';
                    } else {
                        $users[$userId]['first_name'] = $nameSettings[0][1] === 'I' ? $users[$userId]['first_name'][0] : $users[$userId]['first_name'];
                        $users[$userId]['last_name'] = $nameSettings[1][1] === 'I' ? $users[$userId]['last_name'][0] : $users[$userId]['last_name'];
                    }
                }

                $uncachedUsers = array_combine(array_map(fn($id) => 'user-'.$id, array_column($users, 'id')), $users);
                $pool->setMultiple($uncachedUsers);

            }
            $users = array_merge($uncachedUsers, $cachedUsers);

            if (count($users) === 1) {
                $users = current($users);
            }
            return $users;
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    protected function defineValidationConstraints(): ?Constraints\Collection
    {
        return new Constraints\Collection([
            'fields' => [
                'userId'=>new Constraints\Required(),
                'includeAttributes'=>new Constraints\Optional(),
                'includeSettings'=>new Constraints\Optional(),
                'calculateRelationshipWith'=>new Constraints\Optional(),
                'forceRelationship'=>new Constraints\Optional(),
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true
        ]);
    }

    protected function defineDataDefaults(): array
    {
        return [
            'includeAttributes' => false,
            'includeSettings'=> false,
            'calculateRelationshipWith'=> false,
            'forceRelationship'=> false,
        ];
    }
}
