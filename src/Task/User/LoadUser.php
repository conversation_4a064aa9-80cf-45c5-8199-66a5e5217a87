<?php

namespace Clubster\Peregrine\Task\User;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'user.load')]
#[ActivityInterface(prefix: 'user.load')]
class LoadUser extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            $email = $this->getData('email', []);
            $userId = $this->getData('userId', []);

            if (!is_array($email)) {
                $email = [$email];
            }
            if (!is_array($userId)) {
                $userId = [$userId];
            }

            if (empty($email) && empty($userId)) {
                throw new \RuntimeException('Email or User ID is required to load a user');
            }

            $userQuery = $this->database()->select()
                ->from('users.users');
            if (!empty($userId)) {
                $userQuery->where('id', 'in', $userId);
            }

            if (!empty($email)) {
                $userQuery->where('email', 'in', $email);
            }

            $users = $userQuery->fetchAll();

            if (empty($users)) {
                // If no user is found, return an empty response
                Logger::debug('No user found with the provided criteria');
                return new Response(
                    404,
                    ['Content-Type' => 'application/json'],
                    json_encode(['message' => 'User not found'], JSON_THROW_ON_ERROR)
                );
            }

            // Return a successful response
            return (new Response(
                    200,
                    ['Content-Type' => 'application/json']
                ))->withMessage('User loaded successfully')
                    ->withData($users ?? []);
        } catch (\Exception $exception) {
            // Handle exceptions and return an error response
            return new Response(
                500,
                ['Content-Type' => 'application/json'],
                json_encode(['error' => 'An error occurred: ' . $exception->getMessage()], JSON_THROW_ON_ERROR)
            );
        }
    }
}
