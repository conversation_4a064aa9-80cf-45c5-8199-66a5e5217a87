<?php

namespace Clubster\Peregrine\Task\User;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Psr\Http\Message\ServerRequestInterface;
use Ramsey\Uuid\Uuid;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'user.register')]
#[ActivityInterface(prefix: 'user.register')]
class RegisterUser extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        $userId = $this->getData('userId');

        if (empty($userId)) {
            throw new \RuntimeException('User id is required to register a user');
        }

        $encodedPassword = password_hash($this->getData('password'), PASSWORD_BCRYPT, ['cost' => 12]);

        $accessToken = $userId.'-'.(string)Uuid::uuid4();

        $affected = $this->database()->table('users.users')
            ->update([
                'access_token' => $accessToken,
                'is_active' => true,
                'password' => $encodedPassword
            ],[
                'id' => $userId,
                'is_active' => false,
                'password' => null
            ])
            ->run();
        if ($affected === 0) {
            throw new \RuntimeException('User not updated');
        }

        return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))->withMessage('User registered successfully')
            ->withData(['userId' => $userId,'accessToken' => $accessToken]);
    }

}
