<?php
namespace Clubster\Peregrine\Task\User\Notification;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Cycle\Database\Injection\Fragment;
use Cycle\Database\Query\SelectQuery;
use Exception;
use Psr\Http\Message\ServerRequestInterface;
use Temporal\Activity\ActivityInterface;

#[Task(name: 'user.notification.push-token.load')]
#[ActivityInterface(prefix: 'user.notification.push-token.load')]
class LoadPushTokens extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        try {
            // Retrieve user ID from the request data
            $userId = $this->getData('userId', false);
            if (empty($userId)) {
                throw new Exception('User ID is required to load push tokens');
            }
            if (!is_array($userId)) {
                $userId = [$userId];
            }
            // Fetch push tokens from the database
            $pushTokenQuery = $this->database()
                ->select()
                ->from('users.push_tokens')
                ->where('user_id', 'IN', $userId)
                ->where(static function (SelectQuery $query) {
                    $query->where('deleted_at', 'IS', new Fragment('NULL'))
                        ->orWhere('deleted_at', '>', new Fragment('NOW()'));
                })
                ->run();

            $pushTokens = $pushTokenQuery->fetchAll();
            if (empty($pushTokens)) {
                throw new Exception('No push tokens found for the provided user ID(s)');
            }
            // Process the push tokens if needed
            $pushTokens = array_map(function ($token) {
                return [
                    'id' => $token['id'],
                    'user_id' => $token['user_id'],
                    'token' => $token['token'],
                    'device' => $token['device'],
                    'name' => $token['name'],
                    'created_at' => $token['created_at'],
                    'updated_at' => $token['updated_at']
                ];
            }, $pushTokens);

            // Return a successful response
            return (new Response(
                200,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('Push tokens loaded successfully')
                ->withData($pushTokens);

        } catch (Exception $exception) {
            // Handle exceptions and return an error response
            return (new Response(
                500,
                ['Content-Type' => 'application/json']
            ))
                ->withMessage('An error occurred while processing the request')
                ->withError(['error' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine()]);
        }
    }
}
