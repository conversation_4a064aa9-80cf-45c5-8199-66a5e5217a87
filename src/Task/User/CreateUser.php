<?php

namespace Clubster\Peregrine\Task\User;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityHandler;
use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Clubster\Peregrine\Infrastructure\Task\AbstractTask;
use Cycle\Database\Injection\Expression;
use Psr\Http\Message\ServerRequestInterface;
use Ramsey\Uuid\Uuid;
use Temporal\Activity\ActivityInterface;

#[Task(name: "user.create")]
#[ActivityInterface(prefix: 'user.create')]
class CreateUser extends AbstractTask
{
    use ActivityHandler;

    protected function execute(?ServerRequestInterface $request = null): Response
    {
        Logger::debug(sprintf('Executing LoadUser task with data: %s', json_encode($this->getData(), JSON_THROW_ON_ERROR)));

        $email = filter_var(trim($this->getData('email', null)), FILTER_SANITIZE_EMAIL);

        if (empty($email)) {
            throw new \RuntimeException('Email is required to create a user');
        }

        $userId = (string)Uuid::uuid4();

        $this->database()->table('users.users')
            ->insertOne([
                'id' => $userId,
                'email' => $email,
                'first_name' => $this->getData('firstName'),
                'last_name' => $this->getData('lastName'),
                'created_at' => new Expression('NOW()'),
                'updated_at' => new Expression('NOW()'),
                'is_active' => false
            ]);

        $user = $this->get(LoadUser::class)->setData(['email'=>$email])();
        $user = $user->getData()[0]??null;

        // Return a successful response with the created user ID
        return (new Response(
                    200,
                    ['Content-Type' => 'application/json']
                ))
                ->withMessage('User created successfully')
                ->withData(['user' => $user]);
    }
}
