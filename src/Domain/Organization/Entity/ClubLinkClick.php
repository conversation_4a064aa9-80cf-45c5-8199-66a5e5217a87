<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 5/26/2019
 * @time: 11:00 AM
 */

namespace App\Domain\Organization\Entity;


use App\Traits\Blameable;
use App\Traits\EntityTrait;
use App\Traits\Timestampable;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\SoftDeleteable\Traits\SoftDeleteableEntity;
use JMS\Serializer\Annotation as Serializer;
use Gedmo\Mapping\Annotation as Gedmo;

/**
 * Class ClubLinkClick
 * @package App\Domain\Organization\Entity
 * @ORM\Entity()
 * @ORM\Table(name="organizations.club_link_clicks")
 * @Gedmo\SoftDeleteable(fieldName="deletedAt", timeAware=true, hardDelete=false)
 */
class ClubLinkClick
{
    use EntityTrait;
    use SoftDeleteableEntity;
    use Blameable;
    use Timestampable;

    /**
     * @var string
     * @ORM\ManyToOne(targetEntity="App\Domain\Organization\Entity\ClubLink")
     * @Serializer\Groups({"rest","common"})
     */
    private $clubLink;

}