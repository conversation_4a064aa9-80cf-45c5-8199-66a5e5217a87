<?php
/**
 * @author: <PERSON> <<EMAIL>>
 * @date: 5/1/2020
 * @time: 4:10 PM
 */

namespace App\Domain\Post\Entity;

use App\Domain\Notification\Entity\Notification;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation as Serializer;

/**
 * Class PostNotification
 * @package App\Domain\Post\Entity
 * @ORM\Entity()
 * @ORM\Table(name="notifications.post_notifications")
 * @ORM\InheritanceType(value="JOINED")
 *
 */
class PostNotification extends Notification
{
    /**
     * @var Post
     * @ORM\ManyToOne(targetEntity="App\Domain\Post\Entity\Post")
     */
    private $post;

    /**
     * @return string|null
     * @Serializer\VirtualProperty(name="postId")
     * @Serializer\Groups({"rest","common"})
     */
    public function getPostId()
    {
        return $this->post->getId();
    }

    /**
     * @return Post
     */
    public function getPost(): Post
    {
        return $this->post;
    }

    /**
     * @param Post $post
     * @return PostNotification
     */
    public function setPost(Post $post): PostNotification
    {
        $this->post = $post;
        return $this;
    }
}
