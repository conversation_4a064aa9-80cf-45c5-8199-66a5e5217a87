<?php

namespace App\Controller;

use App\Form\User\AvatarType;
use App\Infrastructure\Falcon\FalconRequest;
use Aws\Ecs\EcsClient;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Class DevController
 * @package App\Controller
 * @Route("/dev", name="dev")
 */
class DevController extends BaseController
{
    /**
     * @Route("", name=":index")
     */
    public function index(Request $request)
    {
        $formList = FalconRequest::execute('Form:LoadFormList',[]);

        return $this->render('dev/index.html.twig', [
            'formList' => $formList,
            'formId' => $request->get('formId', null),
        ]);
    }

    /**
     * @Route("/builder", name=":builder")
     */
    public function builder(): Response
    {
        return $this->render('dev/builder.html.twig', [
            'page_title' => 'Form Builder',
            'page_description' => 'Create dynamic forms with drag and drop functionality'
        ]);
    }

    /**
     * @param Request $request
     * @Route("/form/load")
     */
    public function loadForm(Request $request)
    {
        $form = FalconRequest::execute('Form:LoadForm', [
            'formId' => $request->get('formId'),
        ]);

        return new JsonResponse($form);
    }

    /**
     * @param Request $request
     * @Route("/form/save", methods={"POST"})
     */
    public function storeForm(Request $request)
    {
        $content = $request->getContent();

        FalconRequest::execute('Form:StoreForm', json_decode($content, true));

        return new JsonResponse(['success' => true]);
    }

    /**
     * @param Request $request
     * @Route("/test")
     */
    public function test()
    {
        $test = new EcsClient([
            'version' => 'latest',
            'region' => 'us-east-2'
        ]);
        $services = $test->listServices();
        $stop = '';
    }
}
