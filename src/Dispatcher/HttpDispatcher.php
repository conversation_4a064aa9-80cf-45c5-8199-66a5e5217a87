<?php

namespace <PERSON><PERSON>\Peregrine\Dispatcher;

use Clubster\Peregrine\Infrastructure\Response\Response;
use Nyholm\Psr7\Factory\Psr17Factory;
use Clubster\Peregrine\Dispatcher\Http\InputData;
use Clubster\Peregrine\Infrastructure\Cache\Cache;
use Clubster\Peregrine\Infrastructure\DependencyInjection\ServiceLocator\TaskLocator;
use Clubster\Peregrine\Infrastructure\Dispatcher\DispatcherInterface;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\RoadRunnerMode;
use Spiral\RoadRunner\EnvironmentInterface;
use Spiral\RoadRunner\Http\PSR7Worker;
use Spiral\RoadRunner\Worker;

class HttpDispatcher implements DispatcherInterface
{
    private readonly PSR7Worker $worker;

    public function __construct(
        private readonly TaskLocator $taskLocator
    ) {
        $this->worker = new PSR7Worker(
            Worker::create(),
            new Psr17Factory(),
            new Psr17Factory(),
            new Psr17Factory()
        );
    }

    public function canServe(EnvironmentInterface $env): bool
    {
        return $env->getMode() === RoadRunnerMode::Http->value;
    }

    public function serve(): void
    {
        while (true) {
            try {
                $request = $this->worker->waitRequest();
                if ($request === null) {
                    break;
                }

//                $auth = $request->getHeader('Authorization')[0]?? null;
//                if (empty($auth)) {
//                    $this->worker->respond(new Response(
//                        401,
//                        [],
//                        "Unauthorized"
//                    ));
//                    continue;
//                }
//                $clientAuthResponse = $this->taskLocator->getTask('authenticate-client')
//                    ?->setData(['token' => $auth])
//                    ?->handle($request);
//
//                if ($clientAuthResponse->getStatusCode() !== 200) {
//                    $this->worker->respond($clientAuthResponse);
//                    continue;
//                }
//                $clientData = json_decode($clientAuthResponse->getBody()->getContents(), true);
//                $request = $request->withAttribute('userId',$clientData['data']);

                $inputData = new InputData($request);
                if ($inputData->hasData('cache.key')) {
                    // lets try to get the cached response
                    $cacheKey = $inputData->getData('cache.key', false);
                    if (Cache::has($cacheKey)) {
                        $cachedResponse = Cache::get($cacheKey);
                        $cachedResponse['cache'] = [
                            'hit' => true,
                            'key' => $cacheKey,
                            'expires' => Cache::getTTL($cacheKey)['date']??0
                        ];
                        $this->worker->respond(new Response(
                            200,
                            ['Content-Type' => 'application/json'],
                            json_encode($cachedResponse, JSON_THROW_ON_ERROR)
                        ));
                        continue;
                    }

                    Logger::info(sprintf("Cache key %s not found", $cacheKey));
                    if (!$inputData->getData('cache.allowMiss', false)) {
                        $this->worker->respond(new Response(
                            404,
                            [],
                            "Cache key not found"
                        ));
                        continue;
                    }
                }

                $taskName = $inputData->getData('task',null);
                if (empty($taskName)) {
                    $this->worker->respond(new Response(
                        400,
                        [],
                        "No task name provided"
                    ));
                    continue;
                }

//                Logger::debug(sprintf('Serving task %s for client %s:%s', $taskName, $clientData['data']['key'],$clientData['data']['id']));

                if ($this->taskLocator->hasTask($taskName)) {
                    $task = $this->taskLocator->getTask($taskName);
                    if (!$this->isTaskRoutable($task)) {
                        $this->worker->respond(new Response(
                            404,
                            [],
                            "No task found for: $taskName"
                        ));
                        continue;
                    }

                    if (method_exists($task, 'validate')) {
                        $errors = $task->validate($inputData->getData('payload',[]));
                        if (!empty($errors)) {
                            $this->worker->respond(new Response(
                                400,
                                [],
                                json_encode($errors)
                            ));
                            continue;
                        }
                    }

                    if (method_exists($task,'setData')) {
                        $task->setData($inputData->getData('payload',[]));
                    }

                    if (method_exists($task, 'handle')) {
                        $response = $task();
                        $this->worker->respond($response);
                    } else {
                        $this->worker->respond(new Response(
                            501,
                            [],
                            "Task handler not implemented"
                        ));
                    }
                } else {
                    $this->worker->respond(new Response(
                        404,
                        [],
                        "No task found for: $taskName"
                    ));
                }
            } catch (\Throwable $e) {
                $this->worker->respond(new Response(
                    500,
                    [],
                    "Internal Server Error: " . $e->getMessage()
                ));
            }
        }
    }

    private function isTaskRoutable($task): bool
    {
        $taskRefl = new \ReflectionObject($task);
        $attributes = $taskRefl->getAttributes(\Clubster\Peregrine\Infrastructure\Attribute\Task::class);
        if (empty($attributes)) {
            return false;
        }
        $taskAttr = $attributes[0]->newInstance();
        return !$taskAttr->internal;
    }
}
