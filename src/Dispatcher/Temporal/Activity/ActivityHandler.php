<?php

namespace Clubster\Peregrine\Dispatcher\Temporal\Activity;

use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\Peregrine;
use <PERSON>yholm\Psr7\Response;
use Temporal\Activity\ActivityMethod;
use Temporal\DataConverter\Type;
use Temporal\Workflow\ReturnType;

trait ActivityHandler
{
    #[ActivityMethod(name: '')]
    #[ReturnType(Type::TYPE_ARRAY)]
    public function handle($input)
    {
        try {
            $input = json_decode(json_encode($input, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR);
            $request = Peregrine::getInstance()
                ->getRequest()
                ->withParsedBody($input)
                ->withmethod('POST');

            $response = $this->__invoke($request);

            if ($response instanceof Response) {
                if ($response->getStatusCode() !== 200) {
                    throw new \RuntimeException((string)$response->getBody()->getContents());
                }

                $responseData = $response->getData();
                if (empty($responseData)) {
                    $responseData = $response->getBody()->getContents();
                }
                return $responseData;
            }
            throw new \RuntimeException('Invalid response type from activity handler');
        }catch (\Throwable $exception) {
            Logger::error(sprintf('Activity handler error: %s', $exception->getMessage()));
            throw $exception;
        }
    }
}
