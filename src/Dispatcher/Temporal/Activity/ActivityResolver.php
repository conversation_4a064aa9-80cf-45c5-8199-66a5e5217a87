<?php

namespace Clubster\Peregrine\Dispatcher\Temporal\Activity;

use Symfony\Component\DependencyInjection\Attribute\AutowireLocator;

final readonly class ActivityResolver
{
    public function __construct(
        #[AutowireLocator(services: 'clubster.peregrine.activity')]
        private iterable $activities)
    {
        $stop = '';
    }

    public function getActivities(): iterable
    {
        return $this->activities;
    }

    public function getActivityNames(): \Generator
    {
        foreach($this->activities->getProvidedServices() as $activity) {
            yield $activity;
        }
    }

    public function getActivity(string $activityName): object
    {
        try {
            return $this->activities->get($activityName);
        }catch (\Exception $exception) {
            throw new \InvalidArgumentException(sprintf('Activity "%s" not found.', $activityName), 0, $exception);
        }
    }
}
