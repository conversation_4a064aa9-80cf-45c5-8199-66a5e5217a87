<?php

namespace Clubster\Peregrine\Dispatcher\Temporal\Workflow;

use DateInterval;
use Temporal\Activity\ActivityOptions;
use Temporal\Common\RetryOptions;
use Temporal\Workflow;
use VertigoLabs\DataAware\DataAware;
use Vertigo<PERSON>abs\DataAware\DataAwareInterface;

abstract class AbstractWorkflow implements WorkflowInterface, DataAwareInterface
{
    use DataAware;

    protected DataAwareInterface $headers;

    public function setHeaders($headers)
    {
        $this->headers = $headers;
    }

    public function getHeader($key, $default = null)
    {
        return $this->headers->getRawData($key, $default = null);
    }

    public function hasHeader($key)
    {
        return $this->headers->hasRawData($key);
    }

    protected function activityStub($activity, $retries = 3, $timeout = 'PT30S')
    {
        return Workflow::newActivityStub(
            $activity,
            ActivityOptions::new()
                ->withStartToCloseTimeout(new DateInterval($timeout))
                ->withRetryOptions(
                    RetryOptions::new()->withMaximumAttempts($retries)
                )
        );
    }

    protected function runActivity($activity, $input)
    {
        $result = yield $activity->handle($input);
        if (!is_scalar($result) && !is_resource($result)) {
            $result = json_decode(json_encode($result, JSON_THROW_ON_ERROR), true, 512, JSON_THROW_ON_ERROR);
        }
        return $result;
    }

    protected function childWorkflowStub($workflow, $timeout = 'PT1M')
    {
        return Workflow::newChildWorkflowStub(
            $workflow,
            Workflow\ChildWorkflowOptions::new()
                ->withWorkflowExecutionTimeout(new DateInterval($timeout))
        );
    }

    public function __invoke()
    {
        try {
            return $this->execute();
        } catch (\Throwable $exception) {
            throw new \RuntimeException('An error occurred in the workflow: ' . $exception->getMessage(), 0, $exception);
        }
    }

    abstract protected function execute();
}
