<?php

namespace Clubster\Peregrine\Dispatcher\Temporal\Workflow;

use Symfony\Component\DependencyInjection\Attribute\AutowireIterator;
use Symfony\Component\DependencyInjection\Attribute\AutowireLocator;

final readonly class WorkflowResolver
{
    public function __construct(
        #[AutowireLocator(services: 'clubster.peregrine.workflow')]
        private iterable $workflows
    )
    {}

    public function getWorkflows(): iterable
    {
        return $this->workflows;
    }

    public function getWorkflowNames(): \Generator
    {
        foreach ($this->workflows->getProvidedServices() as $workflow) {
            yield $workflow;
        }
    }

    public function getWorkflow(string $workflowName): object
    {
        foreach ($this->workflows as $workflow) {
            if ($workflow::class === $workflowName) {
                return $workflow;
            }
        }
        throw new \InvalidArgumentException(sprintf('Workflow "%s" not found.', $workflow));
    }
}
