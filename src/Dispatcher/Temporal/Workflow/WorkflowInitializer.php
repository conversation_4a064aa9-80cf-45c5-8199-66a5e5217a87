<?php

namespace Clubster\Peregrine\Dispatcher\Temporal\Workflow;

use Temporal\Workflow\WorkflowInit;

trait WorkflowInitializer
{
    #[WorkflowInit]
    public function __construct($input = null)
    {
        if (method_exists($this, 'setData')) {
            $this->setData((array)$input);
        }
        if (method_exists($this, 'initialize')) {
            $this->initialize();
        }
    }
}
