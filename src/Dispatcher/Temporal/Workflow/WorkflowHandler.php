<?php

namespace Clubster\Peregrine\Dispatcher\Temporal\Workflow;

use Temporal\Internal\Workflow\ScopeContext;
use Temporal\Workflow;
use Temporal\Workflow\WorkflowInit;
use Temporal\Workflow\WorkflowMethod;
use VertigoLabs\DataAware\DataAware;
use VertigoLabs\DataAware\DataAwareInterface;

trait WorkflowHandler
{
    #[WorkflowInit]
    public function __construct($input = null)
    {
        $stop = '';
        $input = (array)($input ?? []);
        if (array_key_exists('_headers_', $input)) {
            $headers = new class implements DataAwareInterface {
                use DataAware;
            };
            $headers->setData((array)$input['_headers_']);
            $this->setHeaders($headers);
            unset($input['_headers_']);
        }

        if (method_exists($this, 'setData')) {
            $this->setData((array)$input);
        }

        if (method_exists($this, 'initialize')) {
            $this->initialize();
        }
    }

    #[WorkflowMethod]
    public function handle($input = null)
    {
        $response = $this->__invoke($input);
        return $response;
    }
}
