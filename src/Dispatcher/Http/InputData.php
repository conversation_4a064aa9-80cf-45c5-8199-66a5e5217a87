<?php

namespace Clubster\Peregrine\Dispatcher\Http;

use <PERSON><PERSON>holm\Psr7\Request;
use <PERSON><PERSON>holm\Psr7\ServerRequest;
use Psr\Http\Message\ServerRequestInterface;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Vertigo<PERSON>abs\DataAware\DataAware;
use VertigoLabs\DataAware\DataAwareInterface;

#[Autoconfigure(autowire: false)]
class InputData implements DataAwareInterface
{
    use DataAware;

    public function __construct(ServerRequest|ServerRequestInterface|array $request)
    {
        if (is_array($request)) {
            $this->setData($request);
            return;
        }

        switch ($request->getMethod()) {
            case 'GET':
            case 'POST':
            default:
                $data = $request->getParsedBody();
                if (!$data) {
                    if ($request->hasHeader('Content-Type') && $request->getHeader('Content-Type')[0] === 'application/json') {
                        $data = json_decode($request->getBody()->getContents(), true);
                    }
                }
//                $data = array_merge($data, $request->getQueryParams());
                break;
            case 'PUT':
                throw new \Exception('PUT not implemented');
                break;
            case 'DELETE':
                throw new \Exception('Delete not implemented');
                break;

        }

        $this->setData($data??[]);
    }
}
