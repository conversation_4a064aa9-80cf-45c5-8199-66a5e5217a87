<?php
namespace Clubster\Peregrine\Dispatcher;

use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityResolver;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowResolver;
use Clubster\Peregrine\Infrastructure\Dispatcher\DispatcherInterface;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Infrastructure\RoadRunnerMode;
use Clubster\Peregrine\Workflow\User\CreateUserWorkflow;
use Spiral\RoadRunner\EnvironmentInterface;
use Temporal\Worker\WorkerFactoryInterface;

class TemporalDispatcher implements DispatcherInterface
{
    private WorkerFactoryInterface $worker;

    public function __construct(
        private readonly WorkflowResolver $workflowResolver,
        private readonly ActivityResolver $activityResolver
    )
    {
        Logger::info('TemporalDispatcher initialized');
    }

    public function canServe(EnvironmentInterface $env): bool
    {
        return $env->getMode() === RoadRunnerMode::Temporal->value;
    }

    public function serve(): void
    {
        $factory = \Temporal\WorkerFactory::create();
        $worker = $factory->newWorker();

        $workflows = $this->workflowResolver->getWorkflowNames();
        foreach ($workflows as $workflow) {
            $worker->registerWorkflowTypes($workflow);
        }

        $activities = $this->activityResolver->getActivityNames();
        foreach ($activities as $activityName) {
            $worker->registerActivity($activityName, fn () => $this->activityResolver->getActivity($activityName));
        }

        $factory->run();
    }
}
