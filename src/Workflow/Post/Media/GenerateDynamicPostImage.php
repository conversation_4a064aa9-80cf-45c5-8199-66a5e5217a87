<?php

namespace Clubster\Peregrine\Workflow\Post\Media;

use Clubster\Peregrine\Dispatcher\Temporal\Workflow\AbstractWorkflow;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowHandler;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Task\Filesystem\PutFileToS3;
use Clubster\Peregrine\Task\Post\LoadPost;
use Clubster\Peregrine\Task\Post\Media\ComposeDynamicPostImage;
use Clubster\Peregrine\Task\Post\Media\DetermineLayout;
use Clubster\Peregrine\Task\Post\Media\InvalidateMediaCache;
use Clubster\Peregrine\Task\Post\Media\LoadPostMedia;
use Temporal\Internal\Workflow\ActivityProxy;
use Temporal\Workflow\WorkflowInterface;

#[WorkflowInterface]
class GenerateDynamicPostImage extends AbstractWorkflow
{
    use WorkflowHandler;

    private ActivityProxy|LoadPost $loadPost;
    private ActivityProxy|LoadPostMedia $loadPostMedia;
    private ActivityProxy|DetermineLayout $determineMediaLayout;
    private ActivityProxy|ComposeDynamicPostImage $composeDynamicImage;
    private ActivityProxy|PutFileToS3 $putFileToS3;
    private ActivityProxy|InvalidateMediaCache $invalidateMediaCache;

    public function initialize()
    {
        // s3 load?
        $this->loadPost = $this->activityStub(LoadPost::class,1,);
        $this->loadPostMedia = $this->activityStub(LoadPostMedia::class,1);
        $this->determineMediaLayout = $this->activityStub(DetermineLayout::class,1);
        $this->composeDynamicImage = $this->activityStub(ComposeDynamicPostImage::class,1);
        $this->putFileToS3 = $this->activityStub(PutFileToS3::class,1);
        $this->invalidateMediaCache = $this->activityStub(InvalidateMediaCache::class,1);

    }

    public function execute()
    {
        $originalMemoryLimit = ini_get('memory_limit');
        ini_set('memory_limit', '-1');
        Logger::debug('Setting memory limit to unlimited for dynamic post image generation');

        $postId = $this->getData('postId', null);
        if (empty($postId)) {
            throw new \Exception('Post ID is required to generate dynamic post image');
        }

        // load post
        $post = yield $this->runActivity($this->loadPost, [
            'postId' => $postId
        ]);
        // determine media layout
        $mediaFiles = yield $this->runActivity($this->loadPostMedia, [
            'postId' => $postId
        ]);
        if (empty($mediaFiles) || empty($mediaFiles[$postId])) {
            return [
                'postId' => $postId,
                'imageUrl' => null,
                'status' => 'success',
                'message' => 'No media files found for post ID: ' . $postId
            ];
        }

        $layout = yield $this->runActivity($this->determineMediaLayout, [
            'mediaFiles' => $mediaFiles[$postId],
        ]);
        // generate image
        $image = yield $this->runActivity($this->composeDynamicImage, array_merge($layout, ['postId' => $postId]));
        $imageContent = $image['image'];
        // upload new image to s3
        $s3Response = yield $this->runActivity($this->putFileToS3, [
            'bucket' => $this->getData('bucket', $_ENV['S3_MEDIA_READ_BUCKET']),
            'key' => $postId . '.jpg',
            'base64Content' => $imageContent,
            'contentType' => 'image/jpeg',
            'acl' => 'public-read',
            'metadata' => [
                'postId' => $postId,
                'env'=>$_ENV['API_ENV'],
                'user'=>$post['createdById']
            ]
        ]);

        // invalidate the image cache in cloudfront
//        $cacheResponse = yield $this->runActivity($this->invalidateMediaCache, [
//            'distributionId' => $_ENV['CLOUDFRONT_MEDIA_DISTRIBUTION_ID'],
//            'paths' => [
//                '/' . $postId . '.jpg'
//            ]
//        ]);

        ini_set('memory_limit', $originalMemoryLimit);
        return [
            'postId' => $postId,
            'imageUrl' => $s3Response['Location'] ?? null,
            'status' => 'success',
            'message' => 'Dynamic post image generated successfully'
        ];
    }
}
