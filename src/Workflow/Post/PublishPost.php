<?php

namespace Clubster\Peregrine\Workflow\Post;

use Carbon\Carbon;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\AbstractWorkflow;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowHandler;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Task\Post\LoadPost;
use Clubster\Peregrine\Task\Post\PublishPost as PublishPostActivity;
use Clubster\Peregrine\Task\Socket\Message\PublishMessage;
use Clubster\Peregrine\Workflow\Post\Media\GenerateDynamicPostImage;
use Temporal\Common\RetryOptions;
use Temporal\Internal\Workflow\ActivityProxy;
use Temporal\Internal\Workflow\ChildWorkflowProxy;
use Temporal\Workflow;
use Temporal\Workflow\WorkflowInterface;

#[WorkflowInterface]
class PublishPost extends AbstractWorkflow
{
    use WorkflowHandler;

    private ActivityProxy|LoadPost $loadPost;
    private ActivityProxy|PublishPostActivity $publishPost;
    private ActivityProxy|PublishMessage $publishMessage;
    private ChildWorkflowProxy $generateDynamicPostImage;

    public function initialize()
    {
        $this->loadPost = $this->activityStub(LoadPost::class,1);
        $this->publishPost = $this->activityStub(PublishPostActivity::class, 3);
        $this->generateDynamicPostImage = Workflow::newChildWorkflowStub(
            GenerateDynamicPostImage::class,
            Workflow\ChildWorkflowOptions::new()
                ->withWorkflowExecutionTimeout(new \DateInterval('PT1H'))
                ->withRetryOptions(RetryOptions::new()->withMaximumAttempts(1))
        );
        $this->publishMessage = $this->activityStub(PublishMessage::class, 1);
    }

    public function execute()
    {
        try {
            $postId = $this->getData('postId', null);
            if (empty($postId)) {
                throw new \Exception('Post ID is required to publish post');
            }

            // Call the LoadPost activity
            $post = yield $this->runActivity($this->loadPost,[
                'postId' => $postId,
            ]);

            yield Workflow::timer(3);

            $post = $post[$postId];

            // Call the GenerateDynamicPostImage workflow
            $dynamicImage = yield $this->generateDynamicPostImage->handle([
                'postId' => $postId,
            ]);

//            if ($post['requires_review']??false) {
//                yield Workflow::await($this->approved);
//            }

            // Call the PublishPost activity
            $publishResult = yield $this->runActivity($this->publishPost, [
                'postId' => $postId,
            ]);

            $domChanges = [
                [
                    'selector'=>'#post-stats-status',
                    'innerHtml'=>'Published',
                    'classList'=>['label-status-published'],
                ],
                [
                    'selector'=>'#post-stats-published-at',
                    'innerHtml'=>Carbon::now($post['timezone'])->format('m/d/Y \a\t g:ia (T)'),
                    'classList'=>[],
                ],
            ];

            if ($post['send_notifications'] === true) {
                $domChanges[] = ['post-stats-publish-with-notifications'];
            }

            // Publish the message to the socket
            yield $this->publishMessage->handle([
                'channel' => 'post-statistics:'.$postId,
                'data' => [
                    'postId' => $postId,
                    'type'=>'dom-update',
                    'dom'=>$domChanges
                ],
            ]);

            return $publishResult;
        }catch (\Throwable $throwable) {
            return [
                'message' => 'An error occurred while publishing the post'.$throwable->getMessage(),
                'data' => [
                    'code' => $throwable->getCode(),
                    'message' => $throwable->getMessage(),
                    'file' => $throwable->getFile(),
                    'line' => $throwable->getLine(),
                ],
            ];
        }
    }
}
