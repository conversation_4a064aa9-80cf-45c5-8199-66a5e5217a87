<?php

namespace Clubster\Peregrine\Workflow\User;

use Clubster\Peregrine\Dispatcher\Temporal\Workflow\AbstractWorkflow;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowHandler;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use Clubster\Peregrine\Task\User\CreateUser;
use Clubster\Peregrine\Task\User\LoadUser;
use Temporal\Activity\ActivityOptions;
use Temporal\Common\RetryOptions;
use Temporal\Workflow;
use Temporal\Workflow\WorkflowMethod;

#[Workflow\WorkflowInterface]
class CreateUserWorkflow extends AbstractWorkflow
{
    use WorkflowHandler;

    private $loadUserActivity;
    private $createUserActivity;

    public function initialize()
    {
        // This method can be used for any additional initialization if needed
        Logger::debug('CreateUserWorkflow initialized with data: ' . json_encode($this->getData(), JSON_THROW_ON_ERROR));
        $this->loadUserActivity = Workflow::newActivityStub(
            LoadUser::class,
            ActivityOptions::new()->withStartToCloseTimeout(new \DateInterval('PT30S'))->withRetryOptions(
                RetryOptions::new()->withMaximumAttempts(1)
            )
        );
        $this->createUserActivity = Workflow::newActivityStub(
            CreateUser::class,
            ActivityOptions::new()->withStartToCloseTimeout(new \DateInterval('PT30S'))->withRetryOptions(
                RetryOptions::new()->withMaximumAttempts(5)
            )
        );
    }

    protected function execute()
    {
        if (empty($this->getData('email'))) {
            throw new \Exception('Email is required');
        }

        // First check if user exists
        $existingUserResponse = yield $this->loadUserActivity->handle($this->getData());
        $existingUserResponse = json_decode(json_encode($existingUserResponse), true);
        Logger::debug(sprintf('user.load response: %s', json_encode($existingUserResponse, JSON_THROW_ON_ERROR)));
        $existingUser = $existingUserResponse['data'][0]['user'] ?? [];
        if (!empty($existingUser)) {
            Logger::debug(sprintf('User already exists: %s', json_encode($existingUser, JSON_THROW_ON_ERROR)));
            return $existingUser;
        }

//      Create the user
        $user = yield $this->createUserActivity->handle($this->getData());
        $user = json_decode(json_encode($user), true);
        return $user;
    }
}
