<?php

namespace Clubster\Peregrine\Workflow\Organization\Member;

use Aws\S3\S3Client;
use Carbon\Carbon;
use Clubster\Falcon\Task\Organization\Member\LoadMember;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\AbstractWorkflow;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowHandler;
use Clubster\Peregrine\Task\Falcon\CallFalconTask;
use Clubster\Peregrine\Task\Organization\Member\CreateMember;
use Clubster\Peregrine\Task\Organization\Member\LoadBulkMemberFile;
use Clubster\Peregrine\Task\Post\PublishPost as PublishPostActivity;
use Clubster\Peregrine\Task\Socket\Message\PublishMessage;
use Clubster\Peregrine\Task\User\LoadUser;
use Temporal\Internal\Workflow\ActivityProxy;
use Temporal\Workflow;
use Temporal\Workflow\WorkflowInterface;

#[WorkflowInterface]
class BulkProcessMembers extends AbstractWorkflow
{
    use WorkflowHandler;

    private ActivityProxy|CallFalconTask $callFalconTask;
    private ActivityProxy|PublishMessage $publishMessage;

    public function initialize()
    {
        $this->callFalconTask = $this->activityStub(CallFalconTask::class);
        $this->publishMessage = $this->activityStub(PublishMessage::class, 1);
    }

    protected function execute()
    {
        $organizationId = $this->getData('organizationId', false);
        $members = $this->getData('members');
        $user = $this->getHeader('X-U', null);

        foreach ($members as $member) {
            $activities[] = yield Workflow::async(function () use ($member, $organizationId, $user) {
                return $this->runActivity($this->callFalconTask, [
                    'task' => 'Organization:Member:CreateMember',
                    'data' => array_merge((array)$member, [
                        'organizationId' => $organizationId
                    ]),
                    'headers'=>[
                        'X-U' => $user
                    ]
                ]);
            });
        }

        $results = yield $activities;

        // Publish the message to the socket
        yield $this->runActivity($this->publishMessage,[
            'channel' => 'notifications:'.$user,
            'data' => [
                'message' => 'bulk member upload complete',
            ],
        ]);

        return $results;
    }
}
