<?php

namespace Clubster\Peregrine\Workflow\Organization\Member\Invitation;

use Carbon\Carbon;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\AbstractWorkflow;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowHandler;
use Clubster\Peregrine\Task\Organization\Member\Invitation\InvalidateInvitation;
use Clubster\Peregrine\Task\Organization\Member\Invitation\LoadMemberInvitations;
use Clubster\Peregrine\Task\Organization\Member\LoadMember;
use Clubster\Peregrine\Task\Organization\Settings\LoadOrganizationSettingValue;
use Clubster\Peregrine\Task\Settings\LoadSetting;
use Temporal\Common\RetryOptions;
use Temporal\Internal\Workflow\ActivityProxy;
use Temporal\Internal\Workflow\ChildWorkflowProxy;
use Temporal\Workflow;
use Temporal\Workflow\WorkflowInterface;

#[WorkflowInterface]
class GatherOutdatedMemberInvitations extends AbstractWorkflow
{
    use WorkflowHandler;

    private ActivityProxy|LoadSetting $loadSetting;
    private ActivityProxy|LoadOrganizationSettingValue $organizationSettingValues;
    private ActivityProxy|LoadMember $loadMember;
    private ActivityProxy|LoadMemberInvitations $loadInvitations;
    private ActivityProxy|InvalidateInvitation $invalidateInvitations;
    private ChildWorkflowProxy $reinviteMembers;

    public function initialize()
    {
        $this->loadSetting = $this->activityStub(LoadSetting::class,1);
        $this->organizationSettingValues = $this->activityStub(LoadOrganizationSettingValue::class,1);
        $this->loadMember = $this->activityStub(LoadMember::class,1);
        $this->loadInvitations = $this->activityStub(LoadMemberInvitations::class,1);
        $this->invalidateInvitations = $this->activityStub(InvalidateInvitation::class,1);
        $this->reinviteMembers = Workflow::newChildWorkflowStub(
            InviteMember::class,
            Workflow\ChildWorkflowOptions::new()
                ->withWorkflowExecutionTimeout(new \DateInterval('PT1H'))
                ->withRetryOptions(RetryOptions::new()->withMaximumAttempts(1))
        );
    }

    protected function execute()
    {
        try {
            // load the setting for auto reinvite
            $setting = yield $this->runActivity($this->loadSetting, ['slug' => 'automatically-resend-member-invitations']);

            if (empty($setting['data'])) {
                // No setting found, exit workflow
                return;
            }

            $settingsIds = array_column($setting['data'], 'id');

            // load the values for the setting
            $settingValues = yield $this->runActivity($this->organizationSettingValues, ['settingId' => $settingsIds]);

            if (empty($settingValues)) {
                // No setting values found, exit workflow
                return;
            }

            $organizations = array_map(function ($value) {
                return [
                    'organizationId' => $value['organization_id'],
                    'settingValue' => $value['setting_value'],
                    'timespan' => match ($value['setting_value']) {
                        'weekly' => Carbon::now()->subWeek(),
                        'biweekly' => Carbon::now()->subWeeks(2),
                        'monthly' => Carbon::now()->subMonth(),
                        default => false
                    }
                ];
            }, $settingValues['data']);

            $reinviteTimespans = array_column($organizations, 'timespan', 'organizationId');
            $reinviteTimespans = array_filter($reinviteTimespans);

            // for each bucket, run the reinvite workflow
            $invitations = yield $this->runActivity($this->loadInvitations, [
                'filterOutDeclined' => true,
                'filterOutExpired' => true,
                'filterOutAccepted' => true,
                'includeDeleted' => false,
                'organizationId' => array_keys($reinviteTimespans)
            ]);

            $stop = $invitations;

            if (empty($invitations['data'])) {
                return [
                    'message' => 'No invitations found for reinvitation.',
                    'data' => []
                ];
            }

            $invalidInvitations = array_filter($invitations['data'], function ($invitation) use ($reinviteTimespans) {
                return Carbon::parse($invitation['created_at']) <= $reinviteTimespans[$invitation['organization_id']];
            });
            $validInvitations = array_filter($invitations['data'], function ($invitation) use ($reinviteTimespans) {
                return !(Carbon::parse($invitation['created_at']) <= $reinviteTimespans[$invitation['organization_id']]);
            });

            $invalidMembers = array_column($invalidInvitations, 'member_id', 'member_id');
            $validMembers = array_column($validInvitations, 'member_id', 'member_id');

            $reinviteMembers = array_diff($invalidMembers, $validMembers);
            yield $this->runActivity($this->invalidateInvitations, [
                'invitationId' => array_column($invalidInvitations, 'id')
            ]);

            if (empty($reinviteMembers)) {
                return [
                    'message' => 'No outdated invitations found for reinvitation.',
                    'data' => []
                ];
            }

            $reinviteResult = yield $this->reinviteMembers->handle(['memberIds' => array_keys($reinviteMembers)]);

            return $reinviteResult;
        }catch (\Throwable $e) {
            return [
                'message' => 'Error during auto reinvitation process: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }
}
