<?php

namespace Clubster\Peregrine\Workflow\Organization\Member\Invitation;

use Clubster\Peregrine\Dispatcher\Temporal\Workflow\AbstractWorkflow;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowHandler;
use Clubster\Peregrine\Infrastructure\Peregrine;
use Clubster\Peregrine\Task\Notification\SendNotification;
use Clubster\Peregrine\Task\Organization\Member\Invitation\CreateInvitation;
use Clubster\Peregrine\Task\Organization\Member\Invitation\InvalidateInvitation;
use Clubster\Peregrine\Task\Organization\Member\Invitation\LoadMemberInvitations;
use Clubster\Peregrine\Task\Organization\Member\LoadMember;
use Cycle\Database\DatabaseManager;
use Temporal\Internal\Workflow\ActivityProxy;
use Temporal\Workflow\WorkflowInterface;
use Temporal\Workflow\WorkflowMethod;

#[WorkflowInterface]
class InviteMember extends AbstractWorkflow
{
    use WorkflowHandler;

    private ActivityProxy|LoadMember $loadMembers;
    private ActivityProxy|LoadMemberInvitations $loadMemberInvitations;
    private ActivityProxy|InvalidateInvitation $invalidateInvitations;
    private ActivityProxy|CreateInvitation $createInvitations;
    private ActivityProxy|SendNotification $sendNotification;

    public function initialize()
    {
        $this->loadMembers = $this->activityStub(LoadMember::class);
        $this->loadMemberInvitations = $this->activityStub(LoadMemberInvitations::class);
        $this->invalidateInvitations = $this->activityStub(InvalidateInvitation::class);
        $this->createInvitations = $this->activityStub(CreateInvitation::class);
        $this->sendNotification = $this->activityStub(SendNotification::class);
    }

    protected function execute()
    {
        try {
            // load member invitations
            $memberIds = $this->getData('memberIds', false);
            if (empty($memberIds)) {
                throw new \Exception('Member IDs are required for reinvitation');
            }
            if (!is_array($memberIds)) {
                $memberIds = [$memberIds];
            }

            // Load members to ensure they exist
            $members = yield $this->runActivity($this->loadMembers, ['memberId' => $memberIds]);

            $invitations = yield $this->runActivity($this->loadMemberInvitations, [
                'filterOutDeclined' => true,
                'filterOutExpired' => true,
                'filterOutAccepted' => true,
                'includeDeleted' => false,
                'memberId' => $memberIds
            ]);

            // invalidate invitations
            if (!empty($invitations)) {
                $invitationIds = array_column($invitations, 'id');
                yield $this->runActivity($this->invalidateInvitations, ['invitationId' => $invitationIds]);
            }

            // create invitations
            $newInvitations = 0;
            foreach ($members['data'] as $member) {
                if ($member['status'] === 'accepted') {
                    continue;
                }
                $invitationData = yield $this->runActivity($this->createInvitations, [
                    'memberId' => $member['id'],
                    'organizationId' => $member['organization_id']
                ]);
                yield $this->runActivity($this->sendNotification,[
                    'recipients' => $member['user_id'],
                    'templateSlug' => 'user:org:invitation',
                    'templateData' => [
                        'memberId' => $member['id'],
                        'user'=> [
                            'isActive' => $member['user']['is_active']??false,
                        ],
                        'token'=> $invitationData['data']['invitationId'],
                        'organizationId' => $member['organization_id'],
                        'logoLink' => "https://www.cardinal.dev.clubster.com/invitation/accept/". $invitationData['data']['invitationId'],
                        'acceptUrl' => "https://www.cardinal.dev.clubster.com/invitation/accept/". $invitationData['data']['invitationId'],
                        'declineUrl' => "https://www.cardinal.dev.clubster.com/invitation/decline/". $invitationData['data']['invitationId'],
                    ],
                    'senderOrganization' => $member['organization_id'],
                    'forcedDeliveryMethods' => ['email'],
                    'requireSubscription' => false
                ]);
                $newInvitations++;
            }
            Peregrine::getInstance()->getContainer()->get(DatabaseManager::class)->database()->rollback();
            return [
                'message' => 'Reinvitation process completed successfully',
                'data' => [
                    'membersReinvited' => $newInvitations,
                    'invitationsInvalidated' => !empty($invitations) ? count($invitations) : 0
                ]
            ];
        }catch (\Exception $e) {
            Peregrine::getInstance()->getContainer()->get(DatabaseManager::class)->database()->rollback();
            throw $e;
//            return [
//                'message' => 'Error during reinvitation process: ' . $e->getMessage(),
//                'data' => []
//            ];
        }
    }
}
