<?php

namespace Clubster\Peregrine\Enum\Filesystem;

enum S3Acl: string
{
    case PRIVATE = 'private';
    case PUBLIC_READ = 'public-read';
    case PUBLIC_READ_WRITE = 'public-read-write';
    case AWS_EXEC_READ = 'aws-exec-read';
    case AUTHENTICATED_READ = 'authenticated-read';
    case BUCKET_OWNER_READ = 'bucket-owner-read';
    case BUCKET_OWNER_FULL_CONTROL = 'bucket-owner-full-control';
    case LOG_DELIVERY_WRITE = 'log-delivery-write';
}
