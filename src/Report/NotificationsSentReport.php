<?php

namespace Clubster\Puffin\Report;

use MongoDB\BSON\UTCDateTime;
use MongoDB\Client;
use React\Promise\PromiseInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function React\Promise\all;

class NotificationsSentReport implements ReportInterface
{

    private readonly Client $client;
    private readonly \MongoDB\Collection $collection;

    public function __construct(
        #[Autowire(param: 'mongoDbUri')]
        private readonly string $host,
        #[Autowire(param: 'mongoDbName')]
        private readonly string $database)
    {
        $this->client = new Client($this->host);
        $this->collection = $this->client->selectCollection($this->database, 'notifications_history');
    }
    public function run(\DateTimeInterface $start, ?\DateTimeInterface $end=null)
    {
        if (!is_null($end) && $end < $start) {
            throw new \InvalidArgumentException('End date must be greater than start date');
        }
        if (!is_null($end)) {
            $end = new UTCDateTime($end);
        }

        $start = new UTCDateTime($start);

        $pipeline = [
            [
                '$match' => [
                    'seen_at' => array_filter([
                        '$gte' => $start,
                        '$lte' => $end
                    ])
                ]
            ],
            [
                '$group' => [
                    '_id' => [
                        'sender' => '$notification_data.sender_organization'
                    ],
                    'totalCount' => ['$sum' => 1],
                    'emailCount' => ['$sum' => ['$cond' => [['$eq' => ['$notification_data.delivery_method', 'email']], 1, 0]]],
                    'pushCount' => ['$sum' => ['$cond' => [['$eq' => ['$notification_data.delivery_method', 'push']], 1, 0]]],
                    'internalCount' => ['$sum' => ['$cond' => [['$eq' => ['$notification_data.delivery_method', 'internal']], 1, 0]]],
                ]
            ],
            [
                '$sort' => [
                    'totalCount' => -1
                ]
            ],
            [
                '$group' => [
                    '_id' => null,
                    'senders' => [
                        '$push' => [
                            'sender' => '$_id.sender',
                            'totalCount' => '$totalCount'
                        ]
                    ],
                    'emailCount' => ['$sum' => '$emailCount'],
                    'pushCount' => ['$sum' => '$pushCount'],
                    'internalCount' => ['$sum' => '$internalCount'],
                    'totalCount' => ['$sum' => '$totalCount']
                ]
            ],
            [
                '$project' => [
                    'emailCount' => 1,
                    'pushCount' => 1,
                    'internalCount' => 1,
                    'senders' => ['$slice' => ['$senders', 5]],
                    'totalCount' => 1,
                    '_id' => 0
                ]
            ]
        ];

        $result = $this->collection->aggregate($pipeline);
        $result = $result->toArray();
        $result = array_map(function ($document) {
            return $document->getArrayCopy();
        }, $result);
        return $result;
    }
}
