<?php

namespace Mockingbird\Listener\Http\Compiler;

use Mockingbird\Infrastructure\Task\Compiler\Exception\TaskNamePreviouslyRegisteredException;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class RoutableTaskCompilerPass implements CompilerPassInterface
{
    /**
     * @param ContainerBuilder $container
     * @throws TaskNamePreviouslyRegisteredException
     */
    public function process(ContainerBuilder $container): void
    {
        $tasks = $container->findTaggedServiceIds('routable-task');
        foreach($tasks as $taskId => $tag) {
            $taskRefl = new \ReflectionClass($taskId);
            $taskRouteName = ($taskRefl->newInstanceWithoutConstructor())->getRoutableName();

            if ($container->hasAlias($taskRouteName)) {
                $existingAlias = $container->getAlias($taskRouteName);
                throw new TaskNamePreviouslyRegisteredException($taskRouteName, (string)$existingAlias, $taskId);
            }
            $container->setAlias($taskRouteName, $taskId)->setPublic(true);
        }
    }
}
