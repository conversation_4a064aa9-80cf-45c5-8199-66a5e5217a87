<?php

namespace Mocking<PERSON>\Listener;

use <PERSON>ckingbird\Infrastructure\Listener\Listener;
use <PERSON>ckingbird\Infrastructure\Listener\ListenerInterface;
use <PERSON>cking<PERSON>\Listener\Http\Request;
use <PERSON>ckingbird\Mockingbird;
use Psr\Http\Message\ServerRequestInterface;
use React\EventLoop\Loop;
use React\Http\HttpServer;
use React\Http\Message\Response;
use React\Socket\SocketServer;

class Http extends Listener
{
    private int $handledRequestCount = 0;

    public function initialize(): ListenerInterface
    {
        $this->uri = $this->configuration->getData('uri');

        $this->loop = Loop::get();
        $this->socket = new SocketServer($this->uri, [], $this->loop);
        $this->server = new HttpServer($this);
        $this->server->listen($this->socket);
        return $this;
    }

    public function __invoke(ServerRequestInterface $serverRequest)
    {
        try {
            $this->handledRequestCount++;
            $this->log(sprintf('<comment>Handling HTTP request for <info>%s</info></comment>', $serverRequest->getUri()->getPath()));

            $request = new Request($serverRequest);

            $taskName = $request->determineTask();
            $payload = $request->determinePayload();

            $taskResponse = Mockingbird::Container()->get($taskName)
                                            ->setData($payload)
                                            ->setRequest($request)();

            if (!is_string($taskResponse)) {
                $taskResponse = json_encode($taskResponse, JSON_THROW_ON_ERROR);
            }

            $this->log(sprintf('<info>Task <comment>%s</comment> completed successfully</info>', $taskName));
            $this->log(sprintf('<info>Task response %s</info>', $taskResponse));

            $response = new Response(200, ['Content-Type'=>'application/json'], $taskResponse);
        }catch (\Throwable $exception) {
            $response = new Response(500, ['Content-Type'=>'text/plain'], $exception->getMessage());
        } finally {
            return $response;
        }
    }

    public function run(): void
    {
        $this->loop->run();
    }

    public function getHandledRequestCount(): int
    {
        return $this->handledRequestCount;
    }

}
