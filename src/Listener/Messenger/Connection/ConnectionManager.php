<?php

namespace Mockingbird\Listener\Messenger\Connection;

use Mo<PERSON>bird\Listener\Messenger\Exception\ConnectionNotFoundException;
use Ratchet\WebSocket\WsConnection;

class ConnectionManager
{
    private static array $connectionsByResource = [];
    private static array $connectionsByIdent = [];

    public static function CreateConnection(WsConnection $wsConnection)
    {
        $connection = new Connection($wsConnection);
        self::$connectionsByResource[$connection->getResourceId()] = $connection;
    }

    public static function CloseConnection(WsConnection $wsConnection):void
    {
        $connection = self::LocateConnectionByResource($wsConnection->resourceId);
        unset(
            self::$connectionsByIdent[$connection->getId()],
            self::$connectionsByResource[$connection->getResourceId()]
        );
    }

    public static function IdentConnection($connection, $ident)
    {
        self::$connectionsByIdent[$ident] = $connection;
    }

    /**
     * @throws ConnectionNotFoundException
     */
    public static function LocateConnectionByResource(int $connectionResourceId): Connection
    {
        if (empty(self::$connectionsByResource[$connectionResourceId])) {
            throw new ConnectionNotFoundException();
        }
        return self::$connectionsByResource[$connectionResourceId];
    }
}
