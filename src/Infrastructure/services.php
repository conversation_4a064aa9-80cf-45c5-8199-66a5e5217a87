<?php

use Aws\S3\S3Client;
use Clubster\Peregrine\Dispatcher\Temporal\Activity\ActivityResolver;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\AbstractWorkflow;
use Clubster\Peregrine\Dispatcher\Temporal\Workflow\WorkflowResolver;
use Clubster\Peregrine\Infrastructure\Database\DatabaseConnectionAwareInterface;
use Clubster\Peregrine\Infrastructure\Database\URLConnectionConfig;
use Clubster\Peregrine\Infrastructure\DependencyInjection\ContainerAwareInterface;
use Cycle\Database\Config\DatabaseConfig;
use Cycle\Database\Config\MySQL\TcpConnectionConfig;
use Cycle\Database\Config\MySQLDriverConfig;
use Cycle\Database\Config\PostgresDriverConfig;
use Cycle\Database\DatabaseManager;
use Clubster\Peregrine\Infrastructure\DependencyInjection\ContainerAwareTrait;
use Clubster\Peregrine\Infrastructure\Dispatcher\DispatcherInterface;
use Clubster\Peregrine\Infrastructure\DependencyInjection\ServiceLocator\TaskLocator;
use MongoDB\Client;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use function Symfony\Component\DependencyInjection\Loader\Configurator\service;
use function Symfony\Component\DependencyInjection\Loader\Configurator\tagged_locator;

function getClassesWithAttribute(string $attributeName, ContainerBuilder $containerBuilder)
{
    $classes = [];
    $reflection = new ReflectionClass($containerBuilder->getReflectionClass());
    return $classes;
}

return function (ContainerConfigurator $container) {
    $container->parameters()->set('clubster.peregrine.dispatchers', []);
    $container->parameters()->set('clubster.peregrine.tasks.map', []);

    $services = $container->services()
        ->defaults()
        ->autowire()
        ->autoconfigure()
        ->public();

    $services->instanceof(DispatcherInterface::class)
        ->tag('clubster.peregrine.dispatcher')
        ->public();

    // Add this configuration to inject container into services using ContainerAwareTrait
    $services->instanceof(ContainerAwareTrait::class)
        ->call('setContainer', ['@service_container']);

    $services->set(TaskLocator::class)
        ->public()
        ->args([
            tagged_locator('clubster.peregrine.task'),
            '%clubster.peregrine.tasks.map%'
        ]);

    $services->set(WorkflowResolver::class)
        ->autowire(false)
        ->public()
        ->args([
            tagged_locator('clubster.peregrine.workflow')
        ]);

    $services->set(ActivityResolver::class)
        ->autowire(false)
        ->public()
        ->args([
            tagged_locator('clubster.peregrine.activity')
        ]);

    $services->set(Client::class)->args([
        $_ENV['MONGO_DB_URI']
    ]);

    $services->set(S3Client::class)->args([[
        'version' => $_ENV['S3_VERSION'],
        'region'  => $_ENV['S3_REGION'],
    ]]);

    $services->set(\Aws\CloudFront\CloudFrontClient::class)->args([[
        'version' => $_ENV['S3_VERSION'],
        'region'  => $_ENV['S3_REGION'],
    ]]);

    $services->set('defaultPostgresDnsConnection', URLConnectionConfig::class)
        ->args([
            $_ENV['DATABASE_URL'],
        ])
        ->share(true);

    $services->set('defaultPostgresDriver', PostgresDriverConfig::class)
        ->args([
            service('defaultPostgresDnsConnection')
        ])
        ->share(true);

    $services->set(DatabaseConfig::class)
        ->args([
            [
                'default' => 'default',
                'databases' => [
                    'default' => [
                        'connection' => 'default'
                    ]
                ],
                'connections' => [
                    'default' => service('defaultPostgresDriver')
                ]
            ]
        ])
        ->share(true);

    $services->set(DatabaseManager::class)
        ->args([
            service(DatabaseConfig::class)
        ])
        ->share(true);

    $services->instanceof(AbstractWorkflow::class)
        ->tag('clubster.peregrine.workflow')
        ->autowire(false)
        ->public();

    $services->instanceof(DatabaseConnectionAwareInterface::class)
        ->call('setDatabaseManager', [service(DatabaseManager::class)])
        ->public();

    $services->instanceof(ContainerAwareInterface::class)
        ->call('setContainer', [service('service_container')])
        ->public();

    $services->load('Clubster\\Peregrine\\', __DIR__.'/../../src')
        ->exclude([
            'Peregrine.php',
            'services.php',
            'RoadRunnerMode.php',
            'Logger/**',
            'Attribute/**',
            'DependencyInjection/**',
            'Database/URLConnectionConfig.php',
        ]);
};
