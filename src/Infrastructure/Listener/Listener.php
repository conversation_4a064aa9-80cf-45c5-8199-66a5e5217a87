<?php
declare(strict_types=1);

namespace Mockingbird\Infrastructure\Listener;

use Exception;
use Mockingbird\Infrastructure\Configuration\ConfigurationAware;
use Mockingbird\Mockingbird;
use React\EventLoop\LoopInterface;
use React\Socket\ServerInterface;
use Vertigo<PERSON><PERSON>s\LoggerAware\LoggerAware;

abstract class Listener implements ListenerInterface
{
    use LoggerAware;
    use ConfigurationAware;

    protected string $uri;
    protected LoopInterface $loop;
    protected ServerInterface $socket;
    protected mixed $server;
    protected Mockingbird $mockingbird;

    /**
     * @param Mockingbird $mockingbird
     * @throws Exception
     */
    final public function __construct(Mockingbird $mockingbird)
    {
        $this->mockingbird = $mockingbird;
    }

    /**
     * @return LoopInterface
     */
    public function getLoop(): LoopInterface
    {
        return $this->loop;
    }

    /**
     * @return ServerInterface
     */
    public function getSocket(): ServerInterface
    {
        return $this->socket;
    }

    /**
     * @return string
     */
    public function getUri(): string
    {
        return $this->uri;
    }

    /**
     * @return mixed
     */
    public function getServer(): mixed
    {
        return $this->server;
    }
}
