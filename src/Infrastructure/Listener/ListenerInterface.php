<?php

namespace Mockingbird\Infrastructure\Listener;

use Mockingbird\Infrastructure\Configuration\ConfigurationAwareInterface;
use React\EventLoop\LoopInterface;
use React\Socket\ServerInterface;
use VertigoLabs\LoggerAware\LoggerAwareInterface;

interface ListenerInterface extends LoggerAwareInterface, ConfigurationAwareInterface
{
    public function run(): void;
    public function initialize(): ListenerInterface;
    public function getLoop(): LoopInterface;
    public function getSocket(): ServerInterface;
    public function getUri(): string;
    public function getHandledRequestCount(): int;
}
