<?php
namespace Clubster\Peregrine\Infrastructure;

use Clubster\Peregrine\Infrastructure\Cache\Cache;
use Clubster\Peregrine\Infrastructure\Logger\Logger;
use <PERSON>yholm\Psr7\ServerRequest;
use Spiral\RoadRunner\Environment;
use Symfony\Component\DependencyInjection\ContainerInterface;

class Peregrine
{
    public const BASE_DIR = __DIR__.'/../..';
    public const CACHE_DIR = self::BASE_DIR.'/cache';
    private static Peregrine $instance;
    private static Logger $logger;
    private static Cache $cache;

    public function __construct(
        private readonly Environment $environment,
        private readonly ContainerInterface $container
    )
    {
        self::$instance = $this;
        self::$logger = Logger::getInstance();
        self::$cache = Cache::getInstance();
    }

    public function run()
    {
        $dispatchers = $this->container->getParameter('clubster.peregrine.dispatchers');
        foreach ($dispatchers as $dispatcher) {
            $dispatcher = $this->container->get($dispatcher);
            if ($dispatcher->canServe($this->environment)) {
                $dispatcher->serve();
            }
        }
    }

    public static function getInstance(): Peregrine
    {
        return self::$instance;
    }

    public function getContainer(): ContainerInterface
    {
        return $this->container;
    }

    public function getRequest()
    {
        return new ServerRequest('GET', '/');
    }

}
