<?php

namespace Mockingbird\Infrastructure\Configuration;

use Symfony\Component\Config\Definition\ConfigurationInterface;

trait ConfigurationAware
{
    protected Configuration $configuration;

    public function setConfiguration(Configuration $configuration): ConfigurationAwareInterface
    {
        $this->configuration = $configuration;
        return $this;
    }

    public function getConfiguration(): Configuration
    {
        return $this->configuration;
    }
}
