<?php

namespace Clubster\Peregrine\Infrastructure\Cache;

use <PERSON><PERSON><PERSON>\Goridge\RPC\RPC;
use <PERSON><PERSON><PERSON>\RoadRunner\KeyValue\Factory;
use Spiral\RoadRunner\KeyValue\Serializer\IgbinarySerializer;

class Cache
{
    private static Cache $instance;
    private static $storage;

    public function __construct()
    {
        $rpc = RPC::create('tcp://127.0.0.1:6001');
        $factory = (new Factory($rpc))
                    ->withSerializer(new IgbinarySerializer());
        self::$storage = $factory->select('local');
        self::$instance = $this;
    }

    public static function getInstance(): Cache
    {
        if (!isset(self::$instance)) {
            self::$instance = new static();
        }
        return self::$instance;
    }

    public static function get(string $key, mixed $default = null): mixed
    {
        return self::$storage->get($key);
    }

    public static function getTTL(string $key): mixed
    {
        return self::$storage->getTtl($key);
    }

    public static function set(string $key, mixed $value, null|int|\DateInterval $ttl = null): bool
    {
        return self::$storage->set($key, $value, $ttl);
    }

    public static function delete(string $key): bool
    {
        return self::$storage->delete($key);
    }

    public function clear(): bool
    {
        return self::$storage->clear();
    }

    public static function has(string $key): bool
    {
        return self::$storage->has($key);
    }
}
