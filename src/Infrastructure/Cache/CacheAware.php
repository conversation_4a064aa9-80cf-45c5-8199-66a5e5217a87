<?php

namespace Clubster\Peregrine\Infrastructure\Cache;

trait CacheAware
{
    private function getCache($key, $default): Cache
    {
        return Cache::getInstance()->get($key, $default);
    }

    private function getCacheTTL($key): mixed
    {
        return Cache::getInstance()->getTTL($key);
    }

    private function setCache($key, $value, $ttl = null): bool
    {
        return Cache::getInstance()->set($key, $value, $ttl);
    }

    private function deleteCache($key): bool
    {
        return Cache::getInstance()->delete($key);
    }

    private function hasCache($key): bool
    {
        return Cache::getInstance()->has($key);
    }

    private function supplementWithCache($keys, $callback = null): array
    {
        $cache = Cache::getInstance();
        $result = [];
        foreach ($keys as $key) {
            if ($cache->has($key)) {
                $result[$key] = $cache->get($key);
            } else {
                $result[$key] = $callback ? $callback($key) : null;
                if ($result[$key]) {
                    $cache->set($key, $result[$key]);
                }
            }
        }
        return $result;
    }

}
