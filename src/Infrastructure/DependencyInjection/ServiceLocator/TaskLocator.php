<?php

namespace Clubster\Peregrine\Infrastructure\DependencyInjection\ServiceLocator;

use Clubster\Peregrine\Infrastructure\Peregrine;
use Symfony\Component\DependencyInjection\ServiceLocator;

class TaskLocator
{
    public function __construct(
        private readonly ServiceLocator $serviceLocator,
        private readonly array          $taskMap
    )
    {
    }

    public function getTask(string $name): ?object
    {
        $serviceId = $this->taskMap[$name] ?? null;

        if ($serviceId === null) {
            return null;
        }

        return $this->serviceLocator->get($serviceId);
    }

    public function hasTask(string $name): bool
    {
        return isset($this->taskMap[$name]);
    }

    public function getAllTasks(): array
    {
        return $this->taskMap;
    }
}
