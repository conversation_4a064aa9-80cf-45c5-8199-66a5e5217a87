<?php

namespace Clubster\Peregrine\Infrastructure\DependencyInjection\Compiler;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class DispatcherCompilerPass implements CompilerPassInterface
{

    public function process(ContainerBuilder $container)
    {
        $taggedServices = $container->findTaggedServiceIds('clubster.peregrine.dispatcher');
        $dispatchers = [];
        foreach ($taggedServices as $id => $tags) {
            $dispatchers[] = $id;
        }

        $container->setParameter('clubster.peregrine.dispatchers', $dispatchers);
    }
}
