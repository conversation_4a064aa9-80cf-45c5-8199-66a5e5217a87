<?php

namespace Clubster\Peregrine\Infrastructure\DependencyInjection\Compiler;

use Clubster\Peregrine\Infrastructure\Attribute\Task;
use Clubster\Peregrine\Infrastructure\Peregrine;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;
use Spiral\Tokenizer\ClassLocator;
use Symfony\Component\DependencyInjection\ServiceLocator;
use Symfony\Component\Finder\Finder;

class TaskCompilerPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        $finder = new Finder();
        $finder->files()->in(Peregrine::BASE_DIR.'/src');

        $classLocator = new ClassLocator($finder);
        $taskServices = [];

        foreach ($classLocator->getClasses() as $class) {
            $attribute = $class->getAttributes(Task::class)[0] ?? null;

            if (empty($attribute)) {
                continue;
            }

            /** @var Task $task */
            $task = $attribute->newInstance();

            if ($container->hasDefinition($class->getName())) {
                $definition = $container->getDefinition($class->getName());
                $definition->addTag('clubster.peregrine.task', [
                    'name' => $task->name
                ]);
                $definition->setPublic(true);

                $taskServices[$task->name] = $class->getName();
            }
        }

        $container->setParameter('clubster.peregrine.tasks.map', $taskServices);
    }
}
