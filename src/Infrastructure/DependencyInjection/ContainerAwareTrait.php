<?php

namespace Clubster\Peregrine\Infrastructure\DependencyInjection;

use RuntimeException;
use Clubster\Peregrine\Infrastructure\Peregrine;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Contracts\Service\Attribute\Required;

trait ContainerAwareTrait
{
    protected readonly ContainerInterface $container;

    #[Required]
    public function container(): ContainerInterface
    {
        return $this->getContainer();
    }

    public function setContainer(ContainerInterface $container): void
    {
        $this->container = $container;
    }

    public function getContainer(): ContainerInterface
    {
        if (!isset($this->container)) {
            $this->container = Peregrine::getInstance()->getContainer();
        }

        return $this->container;
    }

    protected function get(string $id, $invalidBehavior = ContainerInterface::EXCEPTION_ON_INVALID_REFERENCE): mixed
    {
        return $this->getContainer()->get($id, $invalidBehavior);
    }

    protected function has(string $id): bool
    {
        return $this->getContainer()->has($id);
    }
}
