<?php

namespace Clubs<PERSON>\Peregrine\Infrastructure\DependencyInjection;

use ProjectServiceContainer;
use RuntimeException;
use Clubster\Peregrine\Infrastructure\DependencyInjection\Compiler\DispatcherCompilerPass;
use Clubster\Peregrine\Infrastructure\DependencyInjection\Compiler\TaskCompilerPass;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ChildDefinition;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Dumper\PhpDumper;
use Symfony\Component\DependencyInjection\Loader\PhpFileLoader;
use Temporal\Activity\ActivityInterface;
use Temporal\Workflow\WorkflowInterface;

class ContainerBuilder
{
    public static function Build(string $baseDir, string $cacheDir, bool $forceRebuild = false): ContainerInterface
    {
        $cachedContainer = $cacheDir.DIRECTORY_SEPARATOR.'container-cache.php';

        if (file_exists($cachedContainer) && is_readable($cachedContainer) && $forceRebuild !== true) {
            require_once $cachedContainer;
            if (class_exists('ProjectServiceContainer', false)) {
                $container = new ProjectServiceContainer();
                return $container;
            }
        }

        $container = new \Symfony\Component\DependencyInjection\ContainerBuilder();
        $container->addCompilerPass(new DispatcherCompilerPass());
        $container->addCompilerPass(new TaskCompilerPass());

        $container->registerAttributeForAutoconfiguration(ActivityInterface::class, static function (ChildDefinition $definition) {
            echo sprintf('Registering activity %s', $definition->getClass()).PHP_EOL;
            $definition->addTag('clubster.peregrine.activity');
        });
        $container->registerAttributeForAutoconfiguration(WorkflowInterface::class, static function (ChildDefinition $definition){
            echo sprintf('Registering workflow %s', $definition->getClass()).PHP_EOL;
            $definition->addTag('clubster.peregrine.workflow');
        });
        $loader = new PhpFileLoader($container, new FileLocator($baseDir.'/src/Infrastructure'));
        $loader->load('services.php');
        $container->compile();

        if (!is_dir($cacheDir)) {
            if (!mkdir($concurrentDirectory = $cacheDir, 0777, true) && !is_dir($concurrentDirectory)) {
                throw new RuntimeException(sprintf('Directory "%s" was not created', $concurrentDirectory));
            }
        }

        if (!is_writable($cacheDir)) {
            echo sprintf('Container cache directory is not writeable: "%s"', $cacheDir);
            return $container;
        }

        if (!file_exists($cachedContainer)) {
            touch($cachedContainer);
        }

        if (!is_writable($cachedContainer)) {
            echo sprintf('Container cache file is not writeable "%s"',$cachedContainer);
            return $container;
        }

        $dumper = new PhpDumper($container);
        file_put_contents($cachedContainer, $dumper->dump());

        return $container;
    }
}
