<?php

namespace Clubster\Peregrine\Infrastructure\Response;

use <PERSON><PERSON>holm\Psr7\Stream;
use VertigoLabs\DataAware\DataAware;
use VertigoLabs\DataAware\DataAwareInterface;

class Response extends \Nyholm\Psr7\Response implements DataAwareInterface
{
    use DataAware;

    private $error;

    public function withMessage(string $message): self
    {
        return $this->withBodyElement('message', $message);
    }

    public function withData(array $data): self
    {
        $this->setData($data);
        return $this->withBodyElement('data',$data);
    }

    public function withError(string|array $error): self
    {
        if (is_string($error)) {
            $error = ['message' => $error];
        }
        $this->error = $error;
        return $this->withBodyElement('error', $error);
    }

    public function withStatusCode(int $code): self
    {
        return $this->withStatus($code);
    }

    public function withReasonPhrase(string $phrase): self
    {
        return $this->withStatus($this->getStatusCode(), $phrase);
    }

    public function withHeaders(array $headers): self
    {
        foreach ($headers as $name => $value) {
            $res = $this->withHeader($name, $value);
        }
        return $this;
    }

    public function withBodyElement($key, $data)
    {
        $currentBody = $this->getBody()->getContents();
        if ($currentBody) {
            $currentBody = json_decode($currentBody, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \RuntimeException('Invalid JSON in response body');
            }
        } else {
            $currentBody = [];
        }
        $currentBody[$key] = $data;
        $body = json_encode($currentBody, JSON_THROW_ON_ERROR);

        return $this->withBody(Stream::create($body));
    }

    public function hasError()
    {
        return !empty($this->error);
    }

    public function getError()
    {
        return $this->error;
    }
}
