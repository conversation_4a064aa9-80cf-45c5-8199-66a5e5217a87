<?php

namespace Clubster\Peregrine\Infrastructure\Logger;

use <PERSON><PERSON><PERSON><PERSON>\Logger\Logger as LogClient;
use Spiral\Goridge\RPC\RPC;

class Logger
{
    private ?LogClient $logClient = null;
    private static Logger $instance;

    private function __construct()
    {
        // Initialize logger
        $rpc = RPC::create('tcp://127.0.0.1:6001');
        $this->logClient = new LogClient($rpc);
    }

    public static function getInstance()
    {
        if (!isset(self::$instance)) {
            self::$instance = new static();
        }
        return self::$instance;
    }

    public static function debug(string $message, ?array $context = [])
    {
        self::$instance->logClient->debug($message, $context);
    }

    public static function error(string $message, ?array $context = [])
    {
        self::$instance->logClient->error($message, $context);
    }

    public static function info(string $message, ?array $context = [])
    {
        self::$instance->logClient->info($message, $context);
    }

    public static function warning(string $message, ?array $context = [])
    {
        self::$instance->logClient->warning($message, $context);
    }

    public static function log(string $message, ?array $context = [])
    {
        self::$instance->logClient->log($message, $context);
    }
}
