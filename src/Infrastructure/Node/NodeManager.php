<?php

namespace Clubster\Nightingale\Infrastructure\Node;

use <PERSON><PERSON>\Nightingale\Infrastructure\Socket\SocketWriter;
use <PERSON><PERSON>\Nightingale\Nightingale;
use MongoDB\Client;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use function React\Async\await;
use function React\Promise\all;
use function React\Promise\resolve;

class NodeManager
{
    private readonly Client $client;
    private readonly \MongoDB\Collection $collection;

    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly SocketWriter $socketWriter,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId,
        #[Autowire(param: 'mongoDbUri')]
        private readonly string $host,
        #[Autowire(param: 'mongoDbName')]
        private readonly string $database,
    )
    {
        $this->client = new Client($this->host);
        $this->collection = $this->client->selectCollection($this->database, 'notification_queue_nodes');
    }

    public function register()
    {
        $result = $this->collection->updateOne(['queue_id'=>$this->queueId], ['$set'=>[
                'queue_id'=>$this->queueId,
                'cluster_name'=>$_ENV['CLUSTER_NAME']??'',
                'last_seen'=>microtime(true),
                'iterations'=>Nightingale::getIteration(),
                'processed_last'=>Nightingale::getProcessedLast(),
                'processed_total'=>Nightingale::getProcessedTotal(),
                'process_times'=>Nightingale::getProcessTimes(),
                'average_process_time'=>Nightingale::getAverageProcessTime(),
                'records_per_iteration'=>(int)$_ENV['INTERVAL_MAX_MESSAGES'],
                'sleep_time'=>(int)$_ENV['INTERVAL_SLEEP_TIME'],
                'empty_queue_sleep_time'=>(int)$_ENV['EMPTY_QUEUE_SLEEP_TIME'],
                'active'=>true,
                'processors'=>array_filter([
                    'email'=>(bool)$_ENV['NOTIFICATION_PROCESSOR_EMAIL'],
                    'push'=>(bool)$_ENV['NOTIFICATION_PROCESSOR_PUSH'],
                    'internal'=>(bool)$_ENV['NOTIFICATION_PROCESSOR_INTERNAL'],
                ]),
            ]
        ], ['upsert'=>true]);

        return $result;
    }

    public function deregister()
    {
        $this->logger->info(sprintf('Deregistering node %s', $this->queueId));
        $result = $this->collection->updateOne(['queue_id'=>$this->queueId], ['$set'=>[
                'active'=>false,
            ]
        ], ['upsert'=>true]);
        $this->logger->info(sprintf('Node %s deregistered', $this->queueId));
//        return await($this->socketWriter->writeData([
//            'type'=>'node_deregistered',
//            'queue'=>['id'=>$this->queueId],
//        ],'admin:notification_queue_statistics')->then(function() use ($result) {
//            return $result;
//        }));
    }
}
