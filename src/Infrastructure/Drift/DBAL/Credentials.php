<?php
namespace Clubster\Puffin\Infrastructure\Drift\DBAL;

class Credentials extends \Drift\DBAL\Credentials
{
    public function __construct(string $connectionString, array $options, int $connections)
    {
        $connectionStringParts = \Safe\parse_url($connectionString);
        $host = $connectionStringParts['host'];
        $port = $connectionStringParts['port'];
        $user = $connectionStringParts['user'];
        $password = $connectionStringParts['pass'];
        $dbName = \ltrim($connectionStringParts['path'], '/');
        parent::__construct($host, $port, $user, $password, $dbName, $options, $connections);
    }
}
