<?php

namespace Clubster\Peregrine\Infrastructure\Database;

use Clubster\Peregrine\Infrastructure\Peregrine;
use Cycle\Database\DatabaseInterface;
use Cycle\Database\DatabaseManager;
use Symfony\Contracts\Service\Attribute\Required;

trait DatabaseConnectionAwareTrait
{
    private readonly DatabaseManager $databaseManager;

    #[Required]
    public function setDatabaseManager(DatabaseManager $databaseManager): void
    {
        $this->databaseManager = $databaseManager;
    }

    public function getDatabaseManager(): DatabaseManager
    {
        return $this->databaseManager;
    }

    public function database(?string $database = 'default'): DatabaseInterface
    {
        return Peregrine::getInstance()->getContainer()->get(DatabaseManager::class)->database();
        return $this->getDatabaseManager()->database($database);
    }
}
