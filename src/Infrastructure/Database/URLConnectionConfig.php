<?php

namespace Clubster\Peregrine\Infrastructure\Database;

use Cycle\Database\Config\Postgres\ConnectionConfig;
use Cycle\Database\Config\Postgres\DsnConnectionConfig;
use Cycle\Database\Config\Support\DataSourceName;

class URLConnectionConfig extends DsnConnectionConfig
{
    public function __construct(
        ?string $url,
        ?string $user = null,
        ?string $password = null,
        array $options = []
    )
    {
        $pathInfo = parse_url($url);
        $dsn = sprintf(
            'pgsql:host=%s;port=%s;dbname=%s',
            $pathInfo['host'] ?? 'localhost',
            $pathInfo['port'] ?? 5432,
            ltrim($pathInfo['path'] ?? '', '/')
        );
        if (isset($pathInfo['user'])) {
            $user = $pathInfo['user'];
        }
        if (isset($pathInfo['pass'])) {
            $password = $pathInfo['pass'];
        }
        parent::__construct($dsn, $user, $password, $options);
    }

}
