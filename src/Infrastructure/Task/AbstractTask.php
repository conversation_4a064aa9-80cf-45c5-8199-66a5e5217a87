<?php

namespace Clubster\Peregrine\Infrastructure\Task;

use Clubster\Peregrine\Dispatcher\Http\InputData;
use Clubster\Peregrine\Infrastructure\Database\DatabaseConnectionAwareInterface;
use Clubster\Peregrine\Infrastructure\Database\DatabaseConnectionAwareTrait;
use Clubster\Peregrine\Infrastructure\DependencyInjection\ContainerAwareInterface;
use Clubster\Peregrine\Infrastructure\DependencyInjection\ContainerAwareTrait;
use Clubster\Peregrine\Infrastructure\Peregrine;
use Clubster\Peregrine\Infrastructure\Response\Response;
use Psr\Http\Message\ServerRequestInterface;
use VertigoLabs\DataAware\DataAware;
use VertigoLabs\DataAware\DataAwareInterface;

abstract class AbstractTask implements TaskInterface, ContainerAwareInterface, DatabaseConnectionAwareInterface, DataAwareInterface
{
    use ContainerAwareTrait;
    use DatabaseConnectionAwareTrait;
    use DataAware;

    final public static function register()
    {
        return Peregrine::getInstance()->getContainer()->get(static::class);
    }

    public function __invoke(?ServerRequestInterface $request = null): Response
    {
        try {
            if ($request !== null) {
                $input = new InputData($request);
                $this->setData($input->getRawData());
            }
            return $this->execute($request);
        } catch (\Throwable $exception) {
            return new Response(
                500,
                ['Content-Type' => 'application/json'],
                json_encode(['error' => 'An error occurred: ' . $exception->getMessage(),'file'=>$exception->getFile(),'line'=>$exception->getLine()], JSON_THROW_ON_ERROR)
            );
        }
    }

    abstract protected function execute(?ServerRequestInterface $request = null): Response;
}
