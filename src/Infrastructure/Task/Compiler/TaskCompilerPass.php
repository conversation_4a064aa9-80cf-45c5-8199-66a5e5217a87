<?php

namespace Mockingbird\Infrastructure\Task\Compiler;

use Mockingbird\Infrastructure\Task\Compiler\Exception\TaskNamePreviouslyRegisteredException;
use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;

class TaskCompilerPass implements CompilerPassInterface
{
    /**
     * @param ContainerBuilder $container
     * @throws TaskNamePreviouslyRegisteredException
     */
    public function process(ContainerBuilder $container): void
    {
        $tasks = $container->findTaggedServiceIds('task');
        foreach($tasks as $taskId => $tag) {
            $taskName = str_replace('\\', ':',substr($taskId, 17));
            if ($container->hasAlias($taskName)) {
                $existingAlias = $container->getAlias($taskName);
                throw new TaskNamePreviouslyRegisteredException($taskName, (string)$existingAlias, $taskId);
            }
            $container->setAlias($taskName, $taskId)->setPublic(true);
        }
    }
}
