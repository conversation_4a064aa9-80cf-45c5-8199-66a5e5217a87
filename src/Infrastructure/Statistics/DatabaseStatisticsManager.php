<?php
namespace Clubster\Nightingale\Infrastructure\Statistics;

use Drift\DBAL\ConnectionPool;
use Psr\Log\LoggerInterface;
use S<PERSON>fony\Component\DependencyInjection\Attribute\Autowire;
use function React\Promise\map;
use function React\Promise\resolve;

class DatabaseStatisticsManager
{
    public function __construct(
        private readonly LoggerInterface $logger,
        private readonly ConnectionPool $database,
        #[Autowire(param: 'nightingale.queue_name')]
        private readonly string $queueId
    ){}

    public function writeStats($records)
    {
        if (empty($records)) {
            $this->logger->info('No notifications to log stats for');
            return resolve();
        }

        return map($records, function ($data) {
            $this->logger->info('Writing statistic record to database');
            $insertData = [
                'id'=>$data['notification_data']['id'],
                'recipient_id'=>$data['notification_data']['recipient_id'],
                'template_id'=>$data['notification_data']['template_id'],
                'status'=>$data['success']?'sent':'failed',
                'send_at'=>'now()',
                'created_at'=>'now()',
                'updated_at'=>'now()',
                'deleted_at'=>null,
                '"_discr"'=>$data['notification_data']['discr'],
                'delivery_method'=>$data['notification_data']['delivery_method'],
                'template_data'=>json_encode($data['notification_data']['template_data']),
                'message_id'=>($data['result']['MessageId']??$data['result']['id']??null),
                'read'=>false,
                'read_at'=>null,
                'sender'=>$data['notification_data']['sender'],
                'sender_organization'=>$data['notification_data']['sender_organization'],
                'reply_to'=>$data['notification_data']['reply_to'],
                'queue_id'=>$this->queueId,
                'recipient'=>$data['notification_data']['recipient'],
            ];
            return $this->database->upsert('notifications.notifications',['id'=>$data['notification_data']['id']], $insertData)
                ->then(function ($result) use ($data) {
                    if (!empty($data['notification_data']['post_id'])) {
                        $insertData = [
                            'id'=>$data['notification_data']['id'],
                            'post_id'=>$data['notification_data']['post_id']
                        ];
                        return $this->database->upsert('notifications.post_notifications',['id'=>$data['notification_data']['id']], $insertData)
                            ->then(function ($result) use ($data) {
                                $this->logger->info('Successfully wrote post notification record to database');
                                return $data;
                            });
                    }
                    return $data;
                })
                ->then(function ($result) use ($data) {
                    $this->logger->info('Successfully wrote statistic record to database');
                    return $data;
                });
        });
    }
}
