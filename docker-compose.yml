services:
  clubster-api-nginx:
    image: clubster-api-nginx:dev
    container_name: clubster-api-nginx
    restart: on-failure
    build:
        context: .
        dockerfile: ${DOCKERFILE:-Dockerfile}
    ports:
        - "${SERVER_PORT}:80"
    healthcheck:
      test: ["CMD", "service", "nginx", "status"]
    volumes:
      - clubster-api_data:/var/www:rw
      - clubster-api_sock:/sock
      - ./deployment/nginx/templates:/etc/nginx/templates
    environment:
      NGINX_ENVSUBST_TEMPLATE_DIR: /etc/nginx/templates
      NGINX_ENVSUBST_TEMPLATE_SUFFIX: .template
      NGINX_ENVSUBST_OUTPUT_DIR: /etc/nginx/conf.d
      CLUBSTER_API_INTERNAL_DNS: ${CLUBSTER_API_INTERNAL_DNS}
    depends_on:
      - clubster-api
    networks:
      - clubster-net

networks:
  clubster-net:
    name: clubster-net
    driver: bridge
