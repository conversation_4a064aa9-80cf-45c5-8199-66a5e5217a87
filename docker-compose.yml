services:
  clubster-nightingale:
    container_name: clubster-nightingale
    image: clubster-nightingale:dev
    build:
        context: .
        dockerfile: ${DOCKERFILE:-Dockerfile}
        target: release
        args:
          DEBUG_CLUBSTER_NIGHTINGALE: ${DEBUG_CLUBSTER_NIGHTINGALE:-false}
    develop:
      watch:
        - action: sync
          path: ./
          target: /app
          ignore:
            - .git
            - .idea
            - node_modules/
            - vendor/
            - var/
    env_file:
      - .env
    environment:
      - DEBUG=false
      - APP_ENV=dev
      - IMAGE_TAG=dev
    networks:
      - clubster-net

networks:
  clubster-net:
    name: clubster-net
    driver: bridge
