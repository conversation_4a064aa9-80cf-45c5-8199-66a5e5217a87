services:
  clubster-mockingbird:
    image: clubster-mockingbird:dev
    container_name: clubster-mockingbird
    build:
        context: .
        dockerfile: ${DOCKERFILE:-Dockerfile}
        target: release
        args:
          - DEBUG_CLUBSTER_MOCKINGBIRD=${DEBUG_CLUBSTER_MOCKINGBIRD:-false}
    env_file:
        - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/Mockingbird:HealthCheck"]
    ports:
      - '6625:6625'
    networks:
      - clubster-net

# all services use the clubster-net network
networks:
  clubster-net:
    name: clubster-net
    driver: bridge
