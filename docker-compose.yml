services:
  clubster-web-nginx:
    image: clubster-web-nginx:dev
    container_name: clubster-web-nginx
    restart: always
    build:
        context: .
        dockerfile: ${DOCKERFILE:-Dockerfile}
    ports:
      - '80:80'
    healthcheck:
      test: ["CMD", "service", "nginx", "status"]
    volumes:
      - clubster-web_data:/var/www:rw
      - clubster-web_sock:/sock
      - ./deployment/nginx/templates:/etc/nginx/templates
    environment:
      NGINX_ENVSUBST_TEMPLATE_DIR: /etc/nginx/templates
      NGINX_ENVSUBST_TEMPLATE_SUFFIX: .template
      NGINX_ENVSUBST_OUTPUT_DIR: /etc/nginx/conf.d
      CLUBSTER_WEB_INTERNAL_DNS: ${CLUBSTER_WEB_INTERNAL_DNS}
    depends_on:
      - clubster-web
    networks:
      - clubster-net

networks:
  clubster-net:
    name: clubster-net
    driver: bridge
