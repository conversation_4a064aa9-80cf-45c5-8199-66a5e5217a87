WITH notification_count AS (
    SELECT
        event_occurrences.id as event_occurrence_id,
        COUNT(notifications.notifications.id) as notification_count,
        COUNT(notifications.notifications.id) FILTER (WHERE notifications.delivery_method = 'email') as email_notification_count,
        COUNT(notifications.notifications.id) FILTER (WHERE notifications.delivery_method = 'push') as push_notification_count
    FROM
        event_occurrences
            INNER JOIN posts ON posts.event_id = event_occurrences.id
            LEFT JOIN notifications.post_notifications ON post_notifications.post_id = posts.id
            LEFT JOIN notifications.notifications ON notifications.notifications.id = notifications.post_notifications."id"
    WHERE
        event_occurrences.id = 'ee51188b-2815-43bb-9441-697c458af215'
    GROUP BY
        event_occurrences.id
),

     audience_count AS (
         SELECT
             event_occurrences.id as event_occurrence_id,
             COUNT(subscriptions.id) AS subscriber_count,
             COUNT(subscriptions.id) FILTER(WHERE subscriptions.allow_email = TRUE) AS email_subscriber_count,
             COUNT(subscriptions.id) FILTER(WHERE subscriptions.allow_push = TRUE) AS push_subscriber_count
         FROM
             event_occurrences
                 INNER JOIN posts.post_audience on post_audience.post_id = event_occurrences.event_id and post_audience.target_type = 'organization'
                 INNER JOIN organizations.subscriptions on posts.post_audience.target_id::uuid = organizations.subscriptions.organization_id
WHERE
    event_occurrences.id = 'ee51188b-2815-43bb-9441-697c458af215'
  AND subscriptions.deleted_at IS NULL
GROUP BY
    event_occurrences.id
    ),

    post_views AS (
SELECT
    event_occurrences.id as event_occurrence_id,
    COUNT(*) as post_views
FROM
    event_occurrences
    INNER JOIN posts ON posts.event_id = event_occurrences.id
    INNER JOIN posts.views on views.post_id = posts.id
WHERE
    event_occurrences.id = 'ee51188b-2815-43bb-9441-697c458af215'
GROUP BY
    event_occurrences.id
    ),

    email_stats AS (
SELECT
    event_occurrences.id as event_occurrence_id,
    COUNT(*) FILTER (WHERE notifications.events.event_type = 'Send') as sent_count,
    COUNT(*) FILTER (WHERE notifications.events.event_type = 'Delivery') as delivery_count,
    COUNT(*) FILTER (WHERE notifications.events.event_type = 'Open') as open_count,
    COUNT(*) FILTER (WHERE notifications.events.event_type = 'Click') as click_count
FROM
    event_occurrences
    INNER JOIN posts ON posts.event_id = event_occurrences.id
    INNER JOIN notifications.post_notifications ON post_notifications.post_id = posts.id
    INNER JOIN notifications.events ON events.notification_id = post_notifications.id
WHERE
    event_occurrences.id = 'ee51188b-2815-43bb-9441-697c458af215'
GROUP BY
    event_occurrences.id
    ),

    occurrence_stats as (
SELECT
    event_occurrences.id as event_occurrence_id,
    event_occurrences.occurrence_index as event_occurrence_index,
    count(*) as event_total_occurrences,
    count(*) FILTER (WHERE event_family.start_at < now()) AS event_occurrence_past_occurrences,
    count(*) FILTER (WHERE event_family.start_at > now()) AS event_occurrence_future_occurrences
FROM
    event_occurrences
    LEFT JOIN event_occurrences as event_family ON event_family.event_family = event_occurrences.event_family
WHERE
    event_occurrences.id = 'ee51188b-2815-43bb-9441-697c458af215'
GROUP BY
    event_occurrences.id
    )

SELECT
    event_occurrences.id as event_occurrence_id,
    occurrence_stats.event_occurrence_index,
    COALESCE(occurrence_stats.event_total_occurrences,0) as event_total_occurrences,
    COALESCE(occurrence_stats.event_occurrence_future_occurrences,0) as event_occurrence_future_occurrences,
    COALESCE(occurrence_stats.event_occurrence_past_occurrences,0) as event_occurrence_future_occurrences,
    posts.subject as post_subject,
    posts.created_at as post_created_at,
    posts.delivery_method as post_delivery_methods,
    event_occurrences.status as post_status,
    event_occurrences.publish_at as post_publish_at,
    COALESCE(notification_count.notification_count,0) as notification_count,
    COALESCE(notification_count.email_notification_count,0) as email_notification_count,
    COALESCE(notification_count.push_notification_count,0) as push_notification_count,
    COALESCE(audience_count.subscriber_count,0) as total_audience_count,
    COALESCE(audience_count.email_subscriber_count,0) as email_audience_count,
    COALESCE(audience_count.push_subscriber_count,0) as push_audience_count,
    COALESCE(post_views.post_views,0) as post_views,
    COALESCE(email_stats.sent_count,0) as sent_count,
    COALESCE(email_stats.delivery_count,0) as delivery_count,
    COALESCE(email_stats.open_count,0) as open_count,
    COALESCE(email_stats.click_count,0) as click_count
FROM
    event_occurrences
        INNER JOIN posts ON posts.id = event_occurrences.event_id
        LEFT JOIN notification_count ON notification_count.event_occurrence_id = event_occurrences.id
        LEFT JOIN audience_count ON audience_count.event_occurrence_id = event_occurrences.id
        LEFT JOIN post_views ON post_views.event_occurrence_id = event_occurrences.id
        LEFT JOIN email_stats ON email_stats.event_occurrence_id = event_occurrences.id
        LEFT JOIN occurrence_stats ON occurrence_stats.event_occurrence_id = event_occurrences.id
WHERE
    event_occurrences.id = 'ee51188b-2815-43bb-9441-697c458af215'
    limit 1
