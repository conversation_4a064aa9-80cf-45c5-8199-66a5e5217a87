[{"Plan": {"Node Type": "Limit", "Parallel Aware": false, "Startup Cost": 9438400.82, "Total Cost": 9438803.4, "Plan Rows": 1, "Plan Width": 728, "Actual Startup Time": 13162.193, "Actual Total Time": 13162.221, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a_5.occurrence_index", "(COALESCE((count(f.*)), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE (f.start_at > now()))), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE (f.start_at < now()))), '0'::bigint))", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at", "(COALESCE((count(n.id)), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE ((n.delivery_method)::text = 'email'::text))), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE ((n.delivery_method)::text = 'push'::text))), '0'::bigint))", "(COALESCE((count(s.id)), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE s.allow_email)), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE s.allow_push)), '0'::bigint))", "(COALESCE((count(*)), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Send'::text))), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Delivery'::text))), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Open'::text))), '0'::bigint))", "(COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Click'::text))), '0'::bigint))"], "Shared Hit Blocks": 49616, "Shared Read Blocks": 12743, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 12516.985, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "InitPlan", "Subplan Name": "CTE anchor", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.85, "Total Cost": 16.89, "Plan Rows": 1, "Plan Width": 169, "Actual Startup Time": 1.107, "Actual Total Time": 1.11, "Actual Rows": 1, "Actual Loops": 1, "Output": ["eo.id", "eo.event_family", "eo.occurrence_index", "eo.status", "eo.publish_at", "p.id", "p.subject", "p.created_at", "p.delivery_method"], "Inner Unique": true, "Shared Hit Blocks": 7, "Shared Read Blocks": 1, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 1.034, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Index Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "event_occurrences_id_uindex", "Relation Name": "event_occurrences", "Schema": "public", "Alias": "eo", "Startup Cost": 0.42, "Total Cost": 8.44, "Plan Rows": 1, "Plan Width": 69, "Actual Startup Time": 1.074, "Actual Total Time": 1.076, "Actual Rows": 1, "Actual Loops": 1, "Output": ["eo.id", "eo.event_id", "eo.start_at", "eo.end_at", "eo.publish_at", "eo.deleted_at", "eo.occurrence_index", "eo.event_series_id", "eo.rsvp_open_at", "eo.rsvp_close_at", "eo.status", "eo.status_reason", "eo.updated_by_id", "eo.updated_at", "eo.event_family", "eo.cancelled_by_id", "eo.deleted_by_id", "eo.cancelled_at"], "Index Cond": "(eo.id = '0ae89da1-4e7a-4ee1-870a-65761df43d0c'::uuid)", "Rows Removed by Index Recheck": 0, "Shared Hit Blocks": 3, "Shared Read Blocks": 1, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 1.034, "I/O Write Time": 0.0}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "posts_pkey", "Relation Name": "posts", "Schema": "public", "Alias": "p", "Startup Cost": 0.42, "Total Cost": 8.44, "Plan Rows": 1, "Plan Width": 116, "Actual Startup Time": 0.026, "Actual Total Time": 0.026, "Actual Rows": 1, "Actual Loops": 1, "Output": ["p.id", "p.event_id", "p.created_by_id", "p.updated_by_id", "p.status", "p.subject", "p.body", "p.publish_at", "p.allow_comments", "p.allow_likes", "p.sticky_until", "p.location", "p.delivery_method", "p.timezone", "p.delete_reason", "p.created_at", "p.updated_at", "p.deleted_at", "p._discr", "p.approved_by_id", "p.requires_approval", "p.approved_at", "p.send_notifications", "p.notifications_sent", "p.raw_input", "p.deleted_by_id"], "Index Cond": "(p.id = eo.event_id)", "Rows Removed by Index Recheck": 0, "Shared Hit Blocks": 4, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}]}, {"Node Type": "Nested Loop", "Parent Relationship": "InitPlan", "Subplan Name": "CTE post_ids", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.42, "Total Cost": 12.5, "Plan Rows": 2, "Plan Width": 16, "Actual Startup Time": 0.016, "Actual Total Time": 0.025, "Actual Rows": 2, "Actual Loops": 1, "Output": ["p_1.id"], "Inner Unique": false, "Shared Hit Blocks": 5, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "anchor", "Alias": "a_6", "Startup Cost": 0.0, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 16, "Actual Startup Time": 0.0, "Actual Total Time": 0.001, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_6.id", "a_6.event_family", "a_6.occurrence_index", "a_6.status", "a_6.publish_at", "a_6.post_id", "a_6.subject", "a_6.created_at", "a_6.delivery_method"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "idx_885dbafa71f7e88b", "Relation Name": "posts", "Schema": "public", "Alias": "p_1", "Startup Cost": 0.42, "Total Cost": 12.46, "Plan Rows": 2, "Plan Width": 32, "Actual Startup Time": 0.014, "Actual Total Time": 0.019, "Actual Rows": 2, "Actual Loops": 1, "Output": ["p_1.id", "p_1.event_id", "p_1.created_by_id", "p_1.updated_by_id", "p_1.status", "p_1.subject", "p_1.body", "p_1.publish_at", "p_1.allow_comments", "p_1.allow_likes", "p_1.sticky_until", "p_1.location", "p_1.delivery_method", "p_1.timezone", "p_1.delete_reason", "p_1.created_at", "p_1.updated_at", "p_1.deleted_at", "p_1._discr", "p_1.approved_by_id", "p_1.requires_approval", "p_1.approved_at", "p_1.send_notifications", "p_1.notifications_sent", "p_1.raw_input", "p_1.deleted_by_id"], "Index Cond": "(p_1.event_id = a_6.id)", "Rows Removed by Index Recheck": 0, "Shared Hit Blocks": 5, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}]}, {"Node Type": "Nested Loop", "Parent Relationship": "InitPlan", "Subplan Name": "CTE notification_ids", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 153.3, "Total Cost": 88470.21, "Plan Rows": 22898, "Plan Width": 16, "Actual Startup Time": 4.409, "Actual Total Time": 19.952, "Actual Rows": 4665, "Actual Loops": 1, "Output": ["pn.id"], "Inner Unique": false, "Shared Hit Blocks": 13, "Shared Read Blocks": 97, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 11.823, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "post_ids", "Alias": "pi_1", "Startup Cost": 0.0, "Total Cost": 0.04, "Plan Rows": 2, "Plan Width": 16, "Actual Startup Time": 0.017, "Actual Total Time": 0.028, "Actual Rows": 2, "Actual Loops": 1, "Output": ["pi_1.id"], "Shared Hit Blocks": 5, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, {"Node Type": "Bitmap Heap Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Relation Name": "post_notifications", "Schema": "notifications", "Alias": "pn", "Startup Cost": 153.3, "Total Cost": 44120.59, "Plan Rows": 11449, "Plan Width": 32, "Actual Startup Time": 4.956, "Actual Total Time": 8.803, "Actual Rows": 2332, "Actual Loops": 2, "Output": ["pn.id", "pn.post_id"], "Recheck Cond": "(pn.post_id = pi_1.id)", "Rows Removed by Index Recheck": 0, "Exact Heap Blocks": 90, "Lossy Heap Blocks": 0, "Shared Hit Blocks": 8, "Shared Read Blocks": 97, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 11.823, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Bitmap Index Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "Index Name": "idx_f3596e954b89032c", "Startup Cost": 0.0, "Total Cost": 150.44, "Plan Rows": 11449, "Plan Width": 0, "Actual Startup Time": 3.204, "Actual Total Time": 3.204, "Actual Rows": 2332, "Actual Loops": 2, "Index Cond": "(pn.post_id = pi_1.id)", "Shared Hit Blocks": 8, "Shared Read Blocks": 7, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5.601, "I/O Write Time": 0.0}]}]}, {"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Left", "Startup Cost": 9349901.23, "Total Cost": 9350303.82, "Plan Rows": 1, "Plan Width": 728, "Actual Startup Time": 13162.191, "Actual Total Time": 13162.212, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a_5.occurrence_index", "COALESCE((count(f.*)), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE (f.start_at > now()))), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE (f.start_at < now()))), '0'::bigint)", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at", "COALESCE((count(n.id)), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE ((n.delivery_method)::text = 'email'::text))), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE ((n.delivery_method)::text = 'push'::text))), '0'::bigint)", "COALESCE((count(s.id)), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE s.allow_email)), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE s.allow_push)), '0'::bigint)", "COALESCE((count(*)), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Send'::text))), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Delivery'::text))), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Open'::text))), '0'::bigint)", "COALESCE((count(*) FILTER (WHERE ((e.event_type)::text = 'Click'::text))), '0'::bigint)"], "Inner Unique": false, "Join Filter": "(a_5.id = a.id)", "Rows Removed by Join Filter": 0, "Shared Hit Blocks": 49616, "Shared Read Blocks": 12743, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 12516.985, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Left", "Startup Cost": 9330542.99, "Total Cost": 9330943.84, "Plan Rows": 1, "Plan Width": 700, "Actual Startup Time": 12841.727, "Actual Total Time": 12841.744, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at", "(count(n.id))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'email'::text)))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'push'::text)))", "(count(s.id))", "(count(*) FILTER (WHERE s.allow_email))", "(count(*) FILTER (WHERE s.allow_push))", "(count(*))", "(count(*) FILTER (WHERE ((e.event_type)::text = 'Send'::text)))", "(count(*) FILTER (WHERE ((e.event_type)::text = 'Delivery'::text)))", "(count(*) FILTER (WHERE ((e.event_type)::text = 'Open'::text)))", "(count(*) FILTER (WHERE ((e.event_type)::text = 'Click'::text)))"], "Inner Unique": true, "Join Filter": "(a_4.id = a.id)", "Rows Removed by Join Filter": 0, "Shared Hit Blocks": 38232, "Shared Read Blocks": 12743, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 12516.985, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Left", "Startup Cost": 200558.22, "Total Cost": 200959.04, "Plan Rows": 1, "Plan Width": 668, "Actual Startup Time": 5301.813, "Actual Total Time": 5301.826, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at", "(count(n.id))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'email'::text)))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'push'::text)))", "(count(s.id))", "(count(*) FILTER (WHERE s.allow_email))", "(count(*) FILTER (WHERE s.allow_push))", "(count(*))"], "Inner Unique": true, "Join Filter": "(a_3.id = a.id)", "Rows Removed by Join Filter": 0, "Shared Hit Blocks": 20968, "Shared Read Blocks": 5259, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5145.69, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Left", "Startup Cost": 198725.3, "Total Cost": 199126.09, "Plan Rows": 1, "Plan Width": 660, "Actual Startup Time": 5291.054, "Actual Total Time": 5291.063, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at", "(count(n.id))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'email'::text)))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'push'::text)))", "(count(s.id))", "(count(*) FILTER (WHERE s.allow_email))", "(count(*) FILTER (WHERE s.allow_push))"], "Inner Unique": true, "Join Filter": "(a_2.id = a.id)", "Rows Removed by Join Filter": 0, "Shared Hit Blocks": 19052, "Shared Read Blocks": 5255, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5141.245, "I/O Write Time": 0.0, "Plans": [{"Node Type": "<PERSON><PERSON> Join", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Left", "Startup Cost": 198708.21, "Total Cost": 199108.96, "Plan Rows": 1, "Plan Width": 636, "Actual Startup Time": 5288.813, "Actual Total Time": 5288.819, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at", "(count(n.id))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'email'::text)))", "(count(*) FILTER (WHERE ((n.delivery_method)::text = 'push'::text)))"], "Inner Unique": true, "Merge Cond": "(a.id = a_1.id)", "Shared Hit Blocks": 18190, "Shared Read Blocks": 5255, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5141.245, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Sort", "Parent Relationship": "Outer", "Parallel Aware": false, "Startup Cost": 0.03, "Total Cost": 0.04, "Plan Rows": 1, "Plan Width": 612, "Actual Startup Time": 1.121, "Actual Total Time": 1.121, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at"], "Sort Key": ["a.id"], "Sort Method": "quicksort", "Sort Space Used": 25, "Sort Space Type": "Memory", "Shared Hit Blocks": 7, "Shared Read Blocks": 1, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 1.034, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "anchor", "Alias": "a", "Startup Cost": 0.0, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 612, "Actual Startup Time": 1.111, "Actual Total Time": 1.114, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a.id", "a.subject", "a.created_at", "a.delivery_method", "a.status", "a.publish_at"], "Shared Hit Blocks": 7, "Shared Read Blocks": 1, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 1.034, "I/O Write Time": 0.0}]}, {"Node Type": "Aggregate", "Strategy": "Sorted", "Partial Mode": "Simple", "Parent Relationship": "Inner", "Parallel Aware": false, "Startup Cost": 198708.18, "Total Cost": 199108.9, "Plan Rows": 1, "Plan Width": 40, "Actual Startup Time": 5287.688, "Actual Total Time": 5287.692, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_1.id", "count(n.id)", "count(*) FILTER (WHERE ((n.delivery_method)::text = 'email'::text))", "count(*) FILTER (WHERE ((n.delivery_method)::text = 'push'::text))"], "Group Key": ["a_1.id"], "Shared Hit Blocks": 18183, "Shared Read Blocks": 5254, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5140.211, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Sort", "Parent Relationship": "Outer", "Parallel Aware": false, "Startup Cost": 198708.18, "Total Cost": 198765.42, "Plan Rows": 22898, "Plan Width": 39, "Actual Startup Time": 5286.503, "Actual Total Time": 5286.797, "Actual Rows": 4665, "Actual Loops": 1, "Output": ["a_1.id", "n.id", "n.delivery_method"], "Sort Key": ["a_1.id"], "Sort Method": "quicksort", "Sort Space Used": 557, "Sort Space Type": "Memory", "Shared Hit Blocks": 18183, "Shared Read Blocks": 5254, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5140.211, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Left", "Startup Cost": 0.57, "Total Cost": 197050.03, "Plan Rows": 22898, "Plan Width": 39, "Actual Startup Time": 5.949, "Actual Total Time": 5275.71, "Actual Rows": 4665, "Actual Loops": 1, "Output": ["a_1.id", "n.id", "n.delivery_method"], "Inner Unique": false, "Shared Hit Blocks": 18183, "Shared Read Blocks": 5254, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5140.211, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "anchor", "Alias": "a_1", "Startup Cost": 0.0, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 16, "Actual Startup Time": 0.0, "Actual Total Time": 0.001, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_1.id", "a_1.event_family", "a_1.occurrence_index", "a_1.status", "a_1.publish_at", "a_1.post_id", "a_1.subject", "a_1.created_at", "a_1.delivery_method"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, {"Node Type": "Nested Loop", "Parent Relationship": "Inner", "Parallel Aware": false, "Join Type": "Left", "Startup Cost": 0.57, "Total Cost": 196821.03, "Plan Rows": 22898, "Plan Width": 23, "Actual Startup Time": 5.946, "Actual Total Time": 5272.791, "Actual Rows": 4665, "Actual Loops": 1, "Output": ["n.id", "n.delivery_method"], "Inner Unique": true, "Shared Hit Blocks": 18183, "Shared Read Blocks": 5254, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5140.211, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "notification_ids", "Alias": "ni", "Startup Cost": 0.0, "Total Cost": 457.96, "Plan Rows": 22898, "Plan Width": 16, "Actual Startup Time": 4.412, "Actual Total Time": 26.693, "Actual Rows": 4665, "Actual Loops": 1, "Output": ["ni.id"], "Shared Hit Blocks": 13, "Shared Read Blocks": 97, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 11.823, "I/O Write Time": 0.0}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "notifications_pkey", "Relation Name": "notifications", "Schema": "notifications", "Alias": "n", "Startup Cost": 0.57, "Total Cost": 8.58, "Plan Rows": 1, "Plan Width": 23, "Actual Startup Time": 1.121, "Actual Total Time": 1.121, "Actual Rows": 1, "Actual Loops": 4665, "Output": ["n.id", "n.recipient_id", "n.sender", "n.sender_organization", "n.template_id", "n.reply_to", "n.delivery_method", "n.status", "n.send_at", "n.template_data", "n.message_id", "n.read", "n.read_at", "n.created_at", "n.updated_at", "n.deleted_at", "n._discr", "n.queue_id", "n.recipient", "n.sent_at"], "Index Cond": "(n.id = ni.id)", "Rows Removed by Index Recheck": 0, "Shared Hit Blocks": 18170, "Shared Read Blocks": 5157, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 5128.388, "I/O Write Time": 0.0}]}]}]}]}]}, {"Node Type": "Aggregate", "Strategy": "Hashed", "Partial Mode": "Simple", "Parent Relationship": "Inner", "Parallel Aware": false, "Startup Cost": 17.1, "Total Cost": 17.11, "Plan Rows": 1, "Plan Width": 40, "Actual Startup Time": 2.236, "Actual Total Time": 2.239, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_2.id", "count(s.id)", "count(*) FILTER (WHERE s.allow_email)", "count(*) FILTER (WHERE s.allow_push)"], "Group Key": ["a_2.id"], "Planned Partitions": 0, "HashAgg Batches": 1, "Peak Memory Usage": 24, "Disk Usage": 0, "Shared Hit Blocks": 862, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.85, "Total Cost": 15.09, "Plan Rows": 201, "Plan Width": 34, "Actual Startup Time": 0.671, "Actual Total Time": 1.996, "Actual Rows": 1041, "Actual Loops": 1, "Output": ["a_2.id", "s.id", "s.allow_email", "s.allow_push"], "Inner Unique": false, "Shared Hit Blocks": 862, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.42, "Total Cost": 8.47, "Plan Rows": 1, "Plan Width": 53, "Actual Startup Time": 0.034, "Actual Total Time": 0.037, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_2.id", "pa.target_id"], "Inner Unique": false, "Shared Hit Blocks": 4, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "anchor", "Alias": "a_2", "Startup Cost": 0.0, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 32, "Actual Startup Time": 0.001, "Actual Total Time": 0.002, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_2.id", "a_2.event_family", "a_2.occurrence_index", "a_2.status", "a_2.publish_at", "a_2.post_id", "a_2.subject", "a_2.created_at", "a_2.delivery_method"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "idx_d381a8be4b89032c", "Relation Name": "post_audience", "Schema": "posts", "Alias": "pa", "Startup Cost": 0.42, "Total Cost": 8.45, "Plan Rows": 1, "Plan Width": 53, "Actual Startup Time": 0.03, "Actual Total Time": 0.03, "Actual Rows": 1, "Actual Loops": 1, "Output": ["pa.id", "pa.post_id", "pa.created_by_id", "pa.updated_by_id", "pa.target_id", "pa.target_type", "pa.status", "pa.deleted_at"], "Index Cond": "(pa.post_id = a_2.post_id)", "Rows Removed by Index Recheck": 0, "Filter": "((pa.target_type)::text = 'organization'::text)", "Rows Removed by Filter": 0, "Shared Hit Blocks": 4, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}]}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "idx_e165ecd532c8a3de", "Relation Name": "subscriptions", "Schema": "organizations", "Alias": "s", "Startup Cost": 0.43, "Total Cost": 4.6, "Plan Rows": 201, "Plan Width": 34, "Actual Startup Time": 0.631, "Actual Total Time": 1.8, "Actual Rows": 1041, "Actual Loops": 1, "Output": ["s.id", "s.user_id", "s.organization_id", "s.created_by_id", "s.updated_by_id", "s.allow_email", "s.allow_push", "s.allow_feed", "s.created_at", "s.updated_at", "s.deleted_at", "s.status", "s.bounce_count", "s.last_bounce"], "Index Cond": "(s.organization_id = (pa.target_id)::uuid)", "Rows Removed by Index Recheck": 0, "Filter": "(s.deleted_at IS NULL)", "Rows Removed by Filter": 29, "Shared Hit Blocks": 858, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}]}]}]}, {"Node Type": "Aggregate", "Strategy": "Hashed", "Partial Mode": "Simple", "Parent Relationship": "Inner", "Parallel Aware": false, "Startup Cost": 1832.92, "Total Cost": 1832.93, "Plan Rows": 1, "Plan Width": 24, "Actual Startup Time": 10.755, "Actual Total Time": 10.758, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_3.id", "count(*)"], "Group Key": ["a_3.id"], "Planned Partitions": 0, "HashAgg Batches": 1, "Peak Memory Usage": 24, "Disk Usage": 0, "Shared Hit Blocks": 1916, "Shared Read Blocks": 4, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 4.445, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.57, "Total Cost": 1780.04, "Plan Rows": 10577, "Plan Width": 16, "Actual Startup Time": 3.248, "Actual Total Time": 10.363, "Actual Rows": 1986, "Actual Loops": 1, "Output": ["a_3.id"], "Inner Unique": false, "Shared Hit Blocks": 1916, "Shared Read Blocks": 4, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 4.445, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.0, "Total Cost": 0.08, "Plan Rows": 2, "Plan Width": 32, "Actual Startup Time": 0.004, "Actual Total Time": 0.011, "Actual Rows": 2, "Actual Loops": 1, "Output": ["a_3.id", "pi.id"], "Inner Unique": false, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "anchor", "Alias": "a_3", "Startup Cost": 0.0, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 16, "Actual Startup Time": 0.0, "Actual Total Time": 0.001, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_3.id", "a_3.event_family", "a_3.occurrence_index", "a_3.status", "a_3.publish_at", "a_3.post_id", "a_3.subject", "a_3.created_at", "a_3.delivery_method"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, {"Node Type": "CTE Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "CTE Name": "post_ids", "Alias": "pi", "Startup Cost": 0.0, "Total Cost": 0.04, "Plan Rows": 2, "Plan Width": 16, "Actual Startup Time": 0.001, "Actual Total Time": 0.004, "Actual Rows": 2, "Actual Loops": 1, "Output": ["pi.id"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}]}, {"Node Type": "Index Only <PERSON>an", "Parent Relationship": "Inner", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "idx_638919f24b89032c", "Relation Name": "views", "Schema": "posts", "Alias": "v", "Startup Cost": 0.57, "Total Cost": 837.1, "Plan Rows": 5288, "Plan Width": 16, "Actual Startup Time": 3.044, "Actual Total Time": 5.049, "Actual Rows": 993, "Actual Loops": 2, "Output": ["v.post_id"], "Index Cond": "(v.post_id = pi.id)", "Rows Removed by Index Recheck": 0, "Heap Fetches": 1986, "Shared Hit Blocks": 1916, "Shared Read Blocks": 4, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 4.445, "I/O Write Time": 0.0}]}]}]}, {"Node Type": "Aggregate", "Strategy": "Hashed", "Partial Mode": "Simple", "Parent Relationship": "Inner", "Parallel Aware": false, "Startup Cost": 9129984.76, "Total Cost": 9129984.77, "Plan Rows": 1, "Plan Width": 48, "Actual Startup Time": 7539.91, "Actual Total Time": 7539.914, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_4.id", "count(*) FILTER (WHERE ((e.event_type)::text = 'Send'::text))", "count(*) FILTER (WHERE ((e.event_type)::text = 'Delivery'::text))", "count(*) FILTER (WHERE ((e.event_type)::text = 'Open'::text))", "count(*) FILTER (WHERE ((e.event_type)::text = 'Click'::text))"], "Group Key": ["a_4.id"], "Planned Partitions": 0, "HashAgg Batches": 1, "Peak Memory Usage": 24, "Disk Usage": 0, "Shared Hit Blocks": 17264, "Shared Read Blocks": 7484, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 7371.295, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.57, "Total Cost": 9079544.51, "Plan Rows": 2241789, "Plan Width": 22, "Actual Startup Time": 5.339, "Actual Total Time": 7525.83, "Actual Rows": 6188, "Actual Loops": 1, "Output": ["a_4.id", "e.event_type"], "Inner Unique": false, "Shared Hit Blocks": 17264, "Shared Read Blocks": 7484, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 7371.295, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Nested Loop", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.0, "Total Cost": 686.96, "Plan Rows": 22898, "Plan Width": 32, "Actual Startup Time": 0.004, "Actual Total Time": 7.65, "Actual Rows": 4665, "Actual Loops": 1, "Output": ["a_4.id", "ni_1.id"], "Inner Unique": false, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "anchor", "Alias": "a_4", "Startup Cost": 0.0, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 16, "Actual Startup Time": 0.001, "Actual Total Time": 0.002, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_4.id", "a_4.event_family", "a_4.occurrence_index", "a_4.status", "a_4.publish_at", "a_4.post_id", "a_4.subject", "a_4.created_at", "a_4.delivery_method"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, {"Node Type": "CTE Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "CTE Name": "notification_ids", "Alias": "ni_1", "Startup Cost": 0.0, "Total Cost": 457.96, "Plan Rows": 22898, "Plan Width": 16, "Actual Startup Time": 0.0, "Actual Total Time": 3.988, "Actual Rows": 4665, "Actual Loops": 1, "Output": ["ni_1.id"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}]}, {"Node Type": "Index Scan", "Parent Relationship": "Inner", "Parallel Aware": false, "Scan Direction": "Forward", "Index Name": "events_notification_id_idx", "Relation Name": "events", "Schema": "notifications", "Alias": "e", "Startup Cost": 0.57, "Total Cost": 395.51, "Plan Rows": 98, "Plan Width": 22, "Actual Startup Time": 1.076, "Actual Total Time": 1.608, "Actual Rows": 1, "Actual Loops": 4665, "Output": ["e.id", "e.notification_id", "e.event_type", "e.raw_event_data", "e.created_at", "e.updated_at"], "Index Cond": "(e.notification_id = ni_1.id)", "Rows Removed by Index Recheck": 0, "Shared Hit Blocks": 17264, "Shared Read Blocks": 7484, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 7371.295, "I/O Write Time": 0.0}]}]}]}, {"Node Type": "Aggregate", "Strategy": "Sorted", "Partial Mode": "Simple", "Parent Relationship": "Inner", "Parallel Aware": false, "Startup Cost": 19358.24, "Total Cost": 19359.95, "Plan Rows": 1, "Plan Width": 44, "Actual Startup Time": 320.456, "Actual Total Time": 320.458, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_5.id", "a_5.occurrence_index", "count(f.*)", "count(*) FILTER (WHERE (f.start_at < now()))", "count(*) FILTER (WHERE (f.start_at > now()))"], "Group Key": ["a_5.id", "a_5.occurrence_index"], "Shared Hit Blocks": 11384, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "Sort", "Parent Relationship": "Outer", "Parallel Aware": false, "Startup Cost": 19358.24, "Total Cost": 19358.41, "Plan Rows": 68, "Plan Width": 245, "Actual Startup Time": 320.442, "Actual Total Time": 320.444, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_5.id", "a_5.occurrence_index", "f.*", "f.start_at"], "Sort Key": ["a_5.id", "a_5.occurrence_index"], "Sort Method": "quicksort", "Sort Space Used": 25, "Sort Space Type": "Memory", "Shared Hit Blocks": 11384, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "<PERSON><PERSON> Join", "Parent Relationship": "Outer", "Parallel Aware": false, "Join Type": "Inner", "Startup Cost": 0.03, "Total Cost": 19356.18, "Plan Rows": 68, "Plan Width": 245, "Actual Startup Time": 175.092, "Actual Total Time": 320.426, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_5.id", "a_5.occurrence_index", "f.*", "f.start_at"], "Inner Unique": false, "Hash Cond": "(f.event_family = a_5.event_family)", "Shared Hit Blocks": 11384, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "<PERSON><PERSON>", "Parent Relationship": "Outer", "Parallel Aware": false, "Relation Name": "event_occurrences", "Schema": "public", "Alias": "f", "Startup Cost": 0.0, "Total Cost": 17178.7, "Plan Rows": 580470, "Plan Width": 241, "Actual Startup Time": 0.013, "Actual Total Time": 268.733, "Actual Rows": 580872, "Actual Loops": 1, "Output": ["f.*", "f.start_at", "f.event_family"], "Shared Hit Blocks": 11384, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, {"Node Type": "Hash", "Parent Relationship": "Inner", "Parallel Aware": false, "Startup Cost": 0.02, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 36, "Actual Startup Time": 0.006, "Actual Total Time": 0.006, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_5.id", "a_5.occurrence_index", "a_5.event_family"], "Hash Buckets": 1024, "Original Hash Buckets": 1024, "Hash Batches": 1, "Original Hash Batches": 1, "Peak Memory Usage": 9, "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0, "Plans": [{"Node Type": "CTE Scan", "Parent Relationship": "Outer", "Parallel Aware": false, "CTE Name": "anchor", "Alias": "a_5", "Startup Cost": 0.0, "Total Cost": 0.02, "Plan Rows": 1, "Plan Width": 36, "Actual Startup Time": 0.002, "Actual Total Time": 0.002, "Actual Rows": 1, "Actual Loops": 1, "Output": ["a_5.id", "a_5.occurrence_index", "a_5.event_family"], "Shared Hit Blocks": 0, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}]}]}]}]}]}]}, "Planning": {"Shared Hit Blocks": 220, "Shared Read Blocks": 0, "Shared Dirtied Blocks": 0, "Shared Written Blocks": 0, "Local Hit Blocks": 0, "Local Read Blocks": 0, "Local Dirtied Blocks": 0, "Local Written Blocks": 0, "Temp Read Blocks": 0, "Temp Written Blocks": 0, "I/O Read Time": 0.0, "I/O Write Time": 0.0}, "Planning Time": 1.773, "Triggers": [], "Execution Time": 13162.71}]