var Encore = require('@symfony/webpack-encore');

Encore
// the project directory where compiled assets will be stored
  .setOutputPath('public/assets/')
  // the public path used by the web server to access the previous directory
  .setPublicPath('/assets')
  .cleanupOutputBeforeBuild()
  .enableSourceMaps(!Encore.isProduction())
  .addExternals({
    jquery: 'jQuery',
    // MtrDatepicker: 'MtrDatepicker',
  })
  .addAliases({
    'jquery': require.resolve('jquery'),
  })

  // uncomment to create hashed filenames (e.g. app.abc123.css)
  .enableVersioning(Encore.isProduction())

  // uncomment to define the assets of the project
  .addEntry('js/app', './assets/js/app.js')
  .addEntry('js/timeago', './assets/js/timeago.js')
  .addEntry('js/post-utility', './assets/components/post-utility/post-utility.js')
  .addEntry('js/rsvp-utility', './assets/components/rsvp-utility/rsvp-utility.js')
  .addEntry('js/post-socket', './assets/components/socket/post-socket.js')
  .addEntry('js/calendar', './assets/js/calendar.js')
  .addEntry('js/calendarnav', './assets/js/calendarnav.js')
    // .addEntry('js/hello', './assets/components/hello/index.js')
    .addEntry('js/form', './assets/components/form/index.js')
    .addEntry('js/form-builder', './assets/components/form-builder/index.js')
  // clubster tinymce init & support functions
  .addEntry('tinymce/tinymce', './assets/js/tinymce.js')
  // custom clubster tinymce skin - used for coloring editor UI
  // the tinyMCE libary will access the files through code
  // see assets/components/tinymce/clubster-readme.txt
  .copyFiles({
    from: './assets/components/tinymce/skins',
    to: 'tinymce/skins/[path]/[name].[ext]',
    includeSubdirectories: true,
    pattern: /.*/
  })
  // clubster tinymce css
  .addStyleEntry('css/tinymce', './assets/css/tinymce/tinymce.css')

  .addStyleEntry('css/app', './assets/css/app.scss')
  .addStyleEntry('css/tailwind', './assets/css/tailwind.scss')
  // uncomment if you use Sass/SCSS files
  .enableSassLoader()

  .enablePostCssLoader()

  // uncomment for legacy applications that require $/jQuery as a global variable
  .autoProvidejQuery()
    // Enable TypeScript support
    .enableTypeScriptLoader()
    // Enable React Presets
  .enableReactPreset()

  .configureBabel(function(babelConfig) {
    babelConfig.plugins = ["@babel/plugin-proposal-object-rest-spread","@babel/plugin-proposal-class-properties", "@babel/plugin-transform-runtime"]
  })
;

Encore.getWebpackConfig().resolve = {
    extensions: ['.js', '.jsx', '.ts', '.tsx']
}

Encore.getWebpackConfig().devtool = 'source-map';

module.exports = Encore.getWebpackConfig();
