{% extends 'layout/auth.base.html.twig' %}

{% block leftCol %}
    <div class="content-box">
        <h1>Forms</h1>
        <ul>
        {% for form in formList %}
            <li>&bull; <a href="{{ path('dev:index', {formId: form.id}) }}">{{ form.name }}</a></li>
        {% endfor %}
        </ul>

        <a href="{{ path('dev:builder') }}" class="clubster-button">Form Builder</a>
    </div>
{% endblock %}

{% block rightCol %}
    {% if formId is not null %}
    <h1>Form</h1>

    {# Form with custom container ID #}
        <div class="content-box">
        {% include 'components/form/dynamic-form.html.twig' with {
            'formId': formId,
            'containerId': "form-#{formId}"
        } %}
        </div>
    {% else %}
        <div class="content-box">
            <h1>Select a form to test</h1>
        </div>
    {% endif %}

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('js/form') }}
{% endblock javascripts %}
