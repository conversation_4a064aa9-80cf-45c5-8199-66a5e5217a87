{% extends 'layout/auth.base.html.twig' %}

{% block leftCol %}
    <div class="content-box">
        <h1>Forms</h1>
        <ul>
        {% for form in formList %}
            <li>&bull; <a href="{{ path('dev:index', {formId: form.id}) }}">{{ form.name }}</a></li>
        {% endfor %}
        </ul>

        <a href="{{ path('dev:builder') }}" class="clubster-button">Form Builder</a>
    </div>
{% endblock %}

{% block rightCol %}
    {% if formId is not null %}
    <h1>Form</h1>

    {# Form with custom container ID #}
        <div class="content-box">
        {% include 'components/form/dynamic-form.html.twig' with {
            'formId': formId,
            'containerId': formId,
            'recordRecallCode': formId~'-'~app.user.id,
            'loadRecord': false
        } %}
        </div>
    {% else %}
        <div class="content-box">
            <h1>Select a form to test</h1>
        </div>
    {% endif %}

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('js/form') }}

    {% if formId is not null %}
    <script>
        // Wait for the form system to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Get the form API for the current form
            const form = ClubsterForm.get('{{ formId }}');

            // Example: Handle form submission
            form.onSubmit(function(form, formData, setFormValues) {
                console.log('Form about to submit:', formData);

                // Example: Add a timestamp
                formData.submitted_at = new Date().toISOString();

                setFormValues(formData);
                console.log('Form data after processing:', formData);
                return true; // Allow submission
            });

            // Example: Handle field changes
            form.onChange('f3fd12a9-a5fe-439a-85bf-91f18722e3ec', function(form, field, prevValue, newValue) {
                console.log('f3fd12a9-a5fe-439a-85bf-91f18722e3ec changed from "' + prevValue + '" to "' + newValue + '"');
                return true; // Accept the change
            });

            // Example: Handle form lifecycle events
            form.beforeReady(function(form) {
                console.log('Form is about to load:', form.formId);
                // You can show a loading indicator here
            });

            form.onReady(function(form) {
                console.log('Form is ready:', form.formId);
                console.log('Initial form values:', form.formValues);
                // You can hide loading indicators, set focus, etc.
            });

            // Example: Handle submission results
            form.onSuccess(function(form, responseData) {
                console.log('Form submitted successfully!', responseData);
                alert('Form submitted successfully!');

                // Example: Redirect after successful submission
                // window.location.href = '/success';
            });

            form.onFailure(function(form, errorData) {
                console.error('Form submission failed:', errorData);
                alert('Form submission failed. Please try again.');

                // Example: Show specific error messages
                if (errorData.response && errorData.response.data && errorData.response.data.message) {
                    alert('Error: ' + errorData.response.data.message);
                }
            });

            // Example: Load data from a record
            // Uncomment and modify the record recall code as needed
            /*
            form.loadData('REC123456').then(function() {
                console.log('Record loaded successfully');
                alert('Record data loaded into form');
            }).catch(function(error) {
                console.error('Failed to load record:', error);
                alert('Failed to load record: ' + error.message);
            });
            */

            // Example: Load data based on URL parameter
            const urlParams = new URLSearchParams(window.location.search);
            const recordCode = urlParams.get('recordCode');
            if (recordCode) {
                form.onReady(function() {
                    form.loadData(recordCode).then(function() {
                        console.log('Record loaded from URL parameter:', recordCode);
                    }).catch(function(error) {
                        console.error('Failed to load record from URL:', error);
                    });
                });
            }
        });
    </script>
    {% endif %}
{% endblock javascripts %}
