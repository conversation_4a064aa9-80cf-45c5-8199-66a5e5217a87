{% extends 'layout/auth.base.html.twig' %}

{% block leftCol %}
    <div class="content-box">
        <h1>Forms</h1>
        <ul>
        {% for form in formList %}
            <li>&bull; <a href="{{ path('dev:index', {formId: form.id}) }}">{{ form.name }}</a></li>
        {% endfor %}
        </ul>

        <a href="{{ path('dev:builder') }}" class="clubster-button">Form Builder</a>
    </div>
{% endblock %}

{% block rightCol %}
    {% if formId is not null %}
    <h1>Form</h1>

    {# Form with custom container ID #}
        <div class="content-box">
        {% include 'components/form/dynamic-form.html.twig' with {
            'formId': formId,
            'containerId': "form-#{formId}"
        } %}
        </div>
    {% else %}
        <div class="content-box">
            <h1>Select a form to test</h1>
        </div>
    {% endif %}

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('js/form') }}

    {% if formId is not null %}
    <script>
        // Wait for the form system to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Get the form API for the current form
            const form = ClubsterForm.get('{{ formId }}');

            // Example: Handle form submission
            form.onSubmit(function(form, formData) {
                console.log('Form about to submit:', formData);

                // Example: Add a timestamp
                formData.submitted_at = new Date().toISOString();

                // Example: Validate required fields
                if (!formData.name && !formData.email) {
                    alert('Please fill in at least name or email');
                    return false; // Cancel submission
                }

                // Example: Convert email to lowercase
                if (formData.email) {
                    formData.email = formData.email.toLowerCase();
                }

                console.log('Form data after processing:', formData);
                return true; // Allow submission
            });

            // Example: Handle field changes
            form.onChange('email', function(form, field, prevValue, newValue) {
                console.log('Email changed from "' + prevValue + '" to "' + newValue + '"');

                // Example: Validate email format
                if (newValue && newValue.length > 0) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(newValue)) {
                        alert('Please enter a valid email address');
                        return false; // Cancel the change
                    }
                }

                return true; // Accept the change
            });

            // Example: Handle form lifecycle events
            form.beforeReady(function(form) {
                console.log('Form is about to load:', form.formId);
                // You can show a loading indicator here
            });

            form.onReady(function(form) {
                console.log('Form is ready:', form.formId);
                console.log('Initial form values:', form.formValues);
                // You can hide loading indicators, set focus, etc.
            });

            // Example: Handle submission results
            form.onSuccess(function(form, responseData) {
                console.log('Form submitted successfully!', responseData);
                alert('Form submitted successfully!');

                // Example: Redirect after successful submission
                // window.location.href = '/success';
            });

            form.onFailure(function(form, errorData) {
                console.error('Form submission failed:', errorData);
                alert('Form submission failed. Please try again.');

                // Example: Show specific error messages
                if (errorData.response && errorData.response.data && errorData.response.data.message) {
                    alert('Error: ' + errorData.response.data.message);
                }
            });
        });
    </script>
    {% endif %}
{% endblock javascripts %}
