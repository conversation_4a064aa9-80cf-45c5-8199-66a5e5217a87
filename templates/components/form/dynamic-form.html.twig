{#
  Usage:
        {% include 'components/form/dynamic-form.html.twig' with {
            'formId': formId,
            'containerId': "form-#{formId}",
            'recallCode': "#{formId}-#{app.user.id}",
            'loadData': false
        } %}
#}

{% set containerId = containerId|default('form-' ~ formId) %}

<div
    id="{{ containerId }}"
    data-form-component
    data-form-id="{{ formId }}"
    data-record-recall-code="{{ recallCode|default('') }}"
    data-load-record="{{ loadData|default(false) }}"
    >
</div>
