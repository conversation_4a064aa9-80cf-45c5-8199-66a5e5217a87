#!/usr/bin/env php
<?php
declare(strict_types=1);

require_once('./vendor/autoload.php');

use Clubs<PERSON>\Nightingale\Nightingale;
use Monolog\Handler\StreamHandler;
use Monolog\Level;
use Psr\Log\LoggerInterface;
use React\EventLoop\Loop;
use React\Http\Browser;
use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\PhpFileLoader;
use Symfony\Component\Dotenv\Dotenv;

ini_set('memory_limit',-1);
error_reporting(E_ALL & ~E_DEPRECATED);
set_time_limit(0);

$envFile = __DIR__.'/.env';
if (file_exists($envFile)) {
    $dotEnv = new Dotenv();
    $dotEnv->load($envFile);
}

if (file_exists('/.dockerenv') && empty($_ENV['TASK_ID'])) {
    $hostname = `cat /etc/hostname`;
    $hostname = trim($hostname);
    if (!empty($hostname)) {
        $_ENV['TASK_ID'] = $hostname;
    }
}

\Sentry\init([
    'dsn' => $_ENV['SENTRY_DSN'],
    'environment' => $_ENV['APP_ENV']??'dev',
    // Specify a fixed sample rate
    'traces_sample_rate' => 1.0,
    // Set a sampling rate for profiling - this is relative to traces_sample_rate
    'profiles_sample_rate' => 1.0,
]);

if (!empty($_ENV['ECS_CONTAINER_METADATA_URI_V4'])) {
    $response = file_get_contents($_ENV['ECS_CONTAINER_METADATA_URI_V4'].'/task');

    $responseData = json_decode($response,true);
    $taskArn = $responseData['TaskARN'];
    $taskArnParts = explode('/',$taskArn);
    $taskId = $taskArnParts[count($taskArnParts)-1];
    $clusterName = $taskArnParts[count($taskArnParts)-2];
    $_ENV['TASK_ID'] = $taskId;
    $_ENV['CLUSTER_NAME'] = $clusterName;
    echo json_encode($responseData);
}

$container = new ContainerBuilder();
$loader = new PhpFileLoader($container, new FileLocator(getcwd().'/config'));
$loader->load('services.php');
$container->compile();

$loop = Loop::get();
//$stdioLogger = StdioLogger::create()->withNewLine(true);
$logger = $container->get(LoggerInterface::class);
//$logger->pushHandler((new PsrHandler($stdioLogger))->setFormatter(new LineFormatter()));
$logger->pushHandler(new StreamHandler('php://stdout', Level::Debug));


// Keep processing the data as long as the script is running
\React\Promise\resolve(Nightingale::getInstance($container)->runQueue());
