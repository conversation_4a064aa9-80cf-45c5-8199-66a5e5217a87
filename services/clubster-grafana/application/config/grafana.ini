[paths]
provisioning = /etc/grafana/provisioning

[server]
# To add HTTPS support:
protocol = http
http_addr = 0.0.0.0
http_port = 3000
root_url = %(protocol)s://%(domain)s:%(http_port)s/
;domain = bluejay.clubster.com

[security]
# If you want to embed grafana into an iframe for example
allow_embedding = true
allow_signup = false

[auth]
oauth_auto_login = true

[auth.google]
enabled = true
client_id = ************-66ea9hspma3hknsejqadjusrnkv33787.apps.googleusercontent.com
client_secret = 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
scopes = openid email profile
auth_url = https://accounts.google.com/o/oauth2/v2/auth
token_url = https://oauth2.googleapis.com/token
api_url = https://openidconnect.googleapis.com/v1/userinfo
allowed_domains = clubster.com
redirect_uri = %(protocol)s://%(domain)s:%(http_port)s/login/google
use_pkce = true
allow_sign_up = true

[users]
default_theme = dark
auto_assign_org = true
auto_assign_org_role = Admin

[auth.anonymous]
# Enables anonymous access
enabled = false

[log]
# Either "console", "file", "syslog". Default is "console"
mode = console
level = info

[plugins]
plugin_admin_enabled = true
