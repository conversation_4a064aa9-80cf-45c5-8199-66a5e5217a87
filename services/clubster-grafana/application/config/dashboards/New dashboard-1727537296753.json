{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "panels": [{"datasource": {"default": false, "type": "grafana-postgresql-datasource", "uid": "cdz83g9vdx81sd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "time"}, "properties": [{"id": "unit", "value": "time:MM-DD-YYYY"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "email_total"}, "properties": [{"id": "displayName", "value": "Emails Sent"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "push_total"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "internal_total"}, "properties": [{"id": "displayName", "value": "Internal Sent"}]}]}, "gridPos": {"h": 11, "w": 13, "x": 0, "y": 0}, "id": 1, "options": {"barRadius": 0, "barWidth": 0.43, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "orientation": "auto", "showValue": "always", "stacking": "normal", "tooltip": {"mode": "single", "sort": "none"}, "xField": "time", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "cdz83g9vdx81sd"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\r\n    time_bucket('1 day', created) as \"time\",\r\n    COUNT(*) FILTER (WHERE type = 'email') as email_total,\r\n    COUNT(*) FILTER (WHERE type = 'push') as push_total,\r\n    COUNT(*) FILTER (WHERE type = 'internal') as internal_total\r\nFROM notification_statistics\r\nwhere created between $__timeFrom() and $__timeTo()\r\nGROUP BY \"time\"\r\nORDER BY \"time\" DESC", "refId": "A", "sql": {"columns": [{"name": "SUM", "parameters": [{"name": "count", "type": "functionParameter"}], "type": "function"}, {"parameters": [{"name": "type", "type": "functionParameter"}], "type": "function"}, {"parameters": [{"name": "period", "type": "functionParameter"}], "type": "function"}], "groupBy": [{"property": {"name": "period", "type": "string"}, "type": "groupBy"}, {"property": {"name": "type", "type": "string"}, "type": "groupBy"}], "limit": 50, "orderBy": {"property": {"name": ["period"], "type": "string"}, "type": "property"}, "orderByDirection": "DESC"}, "table": "daily_notification_event_type_count"}], "title": "Notifications Sent", "type": "barchart"}, {"datasource": {"default": false, "type": "grafana-postgresql-datasource", "uid": "cdz83g9vdx81sd"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "time"}, "properties": [{"id": "unit", "value": "time:MM-DD-YYYY"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "email_total"}, "properties": [{"id": "displayName", "value": "Emails Sent"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "push_total"}, "properties": [{"id": "displayName", "value": "<PERSON><PERSON><PERSON>"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "internal_total"}, "properties": [{"id": "displayName", "value": "Internal Sent"}]}]}, "gridPos": {"h": 11, "w": 13, "x": 0, "y": 11}, "id": 2, "options": {"barRadius": 0, "barWidth": 0.43, "fullHighlight": false, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "orientation": "auto", "showValue": "always", "stacking": "normal", "tooltip": {"mode": "single", "sort": "none"}, "xField": "time", "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "pluginVersion": "11.2.0", "targets": [{"datasource": {"type": "grafana-postgresql-datasource", "uid": "cdz83g9vdx81sd"}, "editorMode": "code", "format": "table", "rawQuery": true, "rawSql": "SELECT\r\n    time_bucket('1 month', created) as \"time\",\r\n    COUNT(*) FILTER (WHERE type = 'email') as email_total,\r\n    COUNT(*) FILTER (WHERE type = 'push') as push_total,\r\n    COUNT(*) FILTER (WHERE type = 'internal') as internal_total\r\nFROM notification_statistics\r\nwhere created between date_trunc('month', NOW() - INTERVAL '6 MONTHS') and current_date\r\nGROUP BY \"time\"\r\nORDER BY \"time\" DESC", "refId": "A", "sql": {"columns": [{"name": "SUM", "parameters": [{"name": "count", "type": "functionParameter"}], "type": "function"}, {"parameters": [{"name": "type", "type": "functionParameter"}], "type": "function"}, {"parameters": [{"name": "period", "type": "functionParameter"}], "type": "function"}], "groupBy": [{"property": {"name": "period", "type": "string"}, "type": "groupBy"}, {"property": {"name": "type", "type": "string"}, "type": "groupBy"}], "limit": 50, "orderBy": {"property": {"name": ["period"], "type": "string"}, "type": "property"}, "orderByDirection": "DESC"}, "table": "daily_notification_event_type_count"}], "title": "Notifications Sent", "type": "barchart"}], "schemaVersion": 39, "tags": [], "templating": {"list": []}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "New dashboard", "uid": "cdz83e01w5lhca", "version": 1, "weekStart": ""}