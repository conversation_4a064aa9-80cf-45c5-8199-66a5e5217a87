services:
  skeleton:
    image: 'skeleton:latest'
    container_name: skeleton
    volumes:
      - ./config/skeleton.conf:/usr/local/etc/skeleton/skeleton.conf
      - skeleton_data:/data
    ports:
      - '0000:0000'
    networks:
      - clubster-net

# all services use the clubster-net network
networks:
  clubster-net:
    name: clubster-net
    driver: bridge

# if your service needs volumes
volumes:
  skeleton_data:
    driver: local
    driver_opts:
      type: none
      device: ../infrastructure/volumes
      o: bind
