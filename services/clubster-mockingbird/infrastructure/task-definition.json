{"taskDefinitionArn": "arn:aws:ecs:us-east-2:559585394309:task-definition/bluejay-clubster-mockingbird:1", "containerDefinitions": [{"name": "clubster-mockingbird", "image": "559585394309.dkr.ecr.us-east-2.amazonaws.com/clubster/mockingbird:latest", "cpu": 0, "portMappings": [{"name": "clubster-mockingbird-6625", "containerPort": 6625, "hostPort": 6625, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "SERVER_IP", "value": "0.0.0.0"}, {"name": "SERVER_PORT", "value": "6625"}, {"name": "REDIS_HOST", "value": "clubster-redis-sess.gwnvms.0001.use2.cache.amazonaws.com"}, {"name": "REDIS_DB", "value": "1"}, {"name": "REDIS_PORT", "value": "6379"}, {"name": "MONGO_DB_URI", "value": "mongodb://bluejay:qsUup>GkJ_?_E!<EMAIL>:27017/?authSource=bluejay&retryWrites=false&authMechanism=SCRAM-SHA-1"}, {"name": "DEBUG_CLUBSTER_MOCKINGBIRD", "value": "false"}, {"name": "FALCON_URL", "value": "https://falcon.bluejay.clubster.com:3255"}, {"name": "SERVER_ENV", "value": "bluejay"}, {"name": "XDEBUG_CONFIG", "value": "false"}, {"name": "ROLLBAR_TOKEN", "value": "2e04395469da427ca83c8ecff562b958"}, {"name": "MONGO_DB_NAME", "value": "bluejay"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:us-east-2:559585394309:secret:bluejay/database/endpoint-YvfwqH:DATABASE_ENDPOINT::"}], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/bluejay/clubster-mockingbird", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "bluejay-clubster-mockingbird", "executionRoleArn": "arn:aws:iam::559585394309:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "3072", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2024-09-17T20:58:54.986Z", "registeredBy": "arn:aws:iam::559585394309:user/jaimz", "tags": []}