{"t":{"$date":"2025-08-24T21:15:05.639Z"},"s":"I","c":"<PERSON><PERSON>GOS<PERSON>","id":1000000005,"ctx":"config","msg":"User updated"}
{"t":{"$date":"2025-08-24T21:15:05.649Z"},"s":"I","c":"MONGOSH","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-08-24T21:15:05.650Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":null},"version":"2.5.0","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-04-08T13:06:58.245Z","gitVersion":"2b03591a8cde3171c86f11d4217352266f1b1a9c","nodeVersion":"v20.19.0","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.39","deps":{"nodeDriverVersion":"6.14.2","libmongocryptVersion":"1.13.0","libmongocryptNodeBindingsVersion":"6.3.0","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-08-24T21:15:05.725Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":295,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-08-24T21:15:05.756Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://localhost:27017/clubster-dev?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.5.0","driver":{"name":"nodejs|mongosh","version":"6.14.2|2.5.0"},"devtoolsConnectVersion":"3.4.1","host":"localhost:27017"}}
{"t":{"$date":"2025-08-24T21:15:05.765Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"localhost:27017"}}
{"t":{"$date":"2025-08-24T21:15:05.820Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-08-24T21:15:05.840Z"},"s":"I","c":"MONGOSH","id":1000000004,"ctx":"connect","msg":"Connecting to server","attr":{"userId":null,"telemetryAnonymousId":"670eb22ae033576d71964031","connectionUri":"<mongodb uri>","mongosh_version":"2.5.0","session_id":"68ab80d926001bdb98d861df","is_localhost":true,"is_do_url":false,"is_atlas_url":false,"is_atlas":false,"server_version":"8.0.9","node_version":"v20.19.0","server_os":"linux","server_arch":"x86_64","is_enterprise":false,"auth_type":null,"is_data_federation":false,"is_stream":false,"dl_version":null,"atlas_version":null,"is_genuine":true,"non_genuine_server_name":"mongodb","is_local_atlas":false,"fcv":null,"api_version":null,"api_strict":null,"api_deprecation_errors":null,"atlas_hostname":null}}
{"t":{"$date":"2025-08-24T21:15:05.843Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-08-24T21:15:05.846Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000024,"ctx":"snippets","msg":"Fetching snippet index","attr":{"refreshMode":"allow-cached"}}
{"t":{"$date":"2025-08-24T21:15:05.846Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
{"t":{"$date":"2025-08-24T21:15:05.850Z"},"s":"I","c":"MONGOSH","id":1000000002,"ctx":"repl","msg":"Started REPL","attr":{"version":"2.5.0"}}
{"t":{"$date":"2025-08-24T21:15:05.883Z"},"s":"I","c":"MONGOSH","id":1000000007,"ctx":"repl","msg":"Evaluating input","attr":{"input":"db.runCommand(\"ping\").ok"}}
{"t":{"$date":"2025-08-24T21:15:05.968Z"},"s":"I","c":"MONGOSH","id":1000000011,"ctx":"shell-api","msg":"Performed API call","attr":{"method":"runCommand","class":"Database","db":"clubster-dev","arguments":{"cmd":{"ping":1}}}}
{"t":{"$date":"2025-08-24T21:15:05.980Z"},"s":"I","c":"MONGOSH","id":1000000045,"ctx":"analytics","msg":"Flushed outstanding data","attr":{"flushError":"Trying to persist throttle state before userId is set","flushDuration":0}}
