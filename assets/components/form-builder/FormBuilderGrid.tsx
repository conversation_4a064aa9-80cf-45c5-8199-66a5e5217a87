import React, { useState } from 'react';
import { useDrop, useDrag } from 'react-dnd';
import { useFormBuilder } from './context';
import { FormElement, FormFieldData, FormSectionData, createDefaultField, createDefaultSection } from './types';
import FormField from '../form/FormField';
import FormSection from '../form/FormSection';
import axios from "axios";

// Drop zone component for indicating where items can be dropped
const DropZone: React.FC<{
  onDrop: (item: any) => void;
  index: number;
  isActive: boolean;
  draggedElementIndex?: number;
}> = ({ onDrop, index, isActive, draggedElementIndex }) => {
  const [{ isOver }, drop] = useDrop({
    accept: ['palette-item', 'form-element'],
    drop: (item, monitor) => {
      // Prevent event bubbling to parent drop zones
      monitor.getDropResult() || onDrop(item);
      return { dropped: true };
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  });

  // Don't show drop zones that are siblings to the dragged element
  // (before, at, or immediately after the dragged element's position)
  if (!isActive) return null;

  if (draggedElementIndex !== undefined) {
    // Hide drop zones that would put the element in the same or adjacent position
    if (index === draggedElementIndex || index === draggedElementIndex + 1) {
      return null;
    }
  }

  return (
    <div
      ref={drop}
      style={{
        height: '20px',
        margin: '4px 0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        backgroundColor: 'transparent',
        transition: 'all 0.2s ease'
      }}
    >
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: 0,
          right: 0,
          height: '2px',
          backgroundColor: '#007bff',
          opacity: isOver ? 1 : 0.5,
          transform: 'translateY(-50%)'
        }}
      />
      <span
        style={{
          backgroundColor: '#fff',
          padding: '0 8px',
          fontSize: '10px',
          color: '#007bff',
          fontWeight: 'bold',
          zIndex: 1
        }}
      >
        drop zone
      </span>
    </div>
  );
};

// Section drop zone component for reordering fields within sections
const SectionDropZone: React.FC<{
  onDrop: (item: any) => void;
  index: number;
  isActive: boolean;
  draggedElementIndex?: number;
  sectionId: string;
}> = ({ onDrop, index, isActive, draggedElementIndex, sectionId }) => {
  const [{ isOver }, drop] = useDrop({
    accept: ['palette-item', 'form-element'],
    canDrop: (item: any) => {
      // Don't allow sections to be dropped into sections
      if (item.type === 'palette-item' && item.item.type === 'section') {
        return false;
      }
      if (item.type === 'form-element' && item.element._meta._type === 'section') {
        return false;
      }
      return true;
    },
    drop: (item, monitor) => {
      // Prevent event bubbling to parent drop zones
      monitor.getDropResult() || onDrop(item);
      return { dropped: true, sectionDrop: true };
    },
    hover: (item, monitor) => {
      // Signal that we're hovering over a section drop zone
      return { sectionHover: true };
    },
    collect: (monitor) => ({
      isOver: monitor.isOver() && monitor.canDrop()
    })
  });

  // Don't show drop zones that are siblings to the dragged element within the same section
  if (!isActive) return null;

  // For section drop zones, we need different logic since draggedElementIndex is for main form
  // We'll show all section drop zones when dragging - the specific hiding logic is complex
  // and it's better to show all valid drop targets

  return (
    <div
      ref={drop}
      style={{
        height: '20px',
        margin: '4px 0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        backgroundColor: 'transparent',
        transition: 'all 0.2s ease'
      }}
    >
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: 0,
          right: 0,
          height: '2px',
          backgroundColor: '#28a745',
          opacity: isOver ? 1 : 0.5,
          transform: 'translateY(-50%)'
        }}
      />
      <span
        style={{
          backgroundColor: '#fff',
          padding: '0 8px',
          fontSize: '10px',
          color: '#28a745',
          fontWeight: 'bold',
          zIndex: 1
        }}
      >
        drop zone
      </span>
    </div>
  );
};

// Grid element component for rendering form elements
const GridElement: React.FC<{
  element: FormElement;
  index: number;
  onDelete: (id: string) => void;
  onSelect: (element: FormElement, type: 'field' | 'section') => void;
  isSelected: boolean;
  showDropZones?: boolean;
  draggedElementIndex?: number;
  setShowDropZones?: (show: boolean) => void;
}> = ({ element, index, onDelete, onSelect, isSelected, showDropZones = false, draggedElementIndex, setShowDropZones }) => {
  const isSection = element._meta._type === 'section';
  const sectionData = element as FormSectionData;
  const fieldData = element as FormFieldData;

  // Make elements draggable for rearranging
  const [{ isDragging }, drag] = useDrag({
    type: 'form-element',
    item: {
      type: 'form-element',
      element: element,
      sourceIndex: index,
      sourceSection: isSection ? null : (fieldData._meta.section_id || null)
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(element.id);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect(element, isSection ? 'section' : 'field');
  };

  if (isSection) {
    // Get the context functions
    const { addElement: contextAddElement, moveElement: contextMoveElement, deleteElement: contextDeleteElement, selectElement: contextSelectElement, state: contextState, setDragging } = useFormBuilder();

    // Handle dropping items within section fields
    const handleSectionFieldDrop = (item: any, sectionId: string, targetIndex: number) => {
      if (item.type === 'palette-item') {
        const paletteItem = item.item;
        if (paletteItem.type !== 'section') {
          // Only allow fields to be dropped into sections, not other sections
          const newField = createDefaultField(paletteItem.fieldType!);
          contextAddElement(newField, targetIndex, sectionId);
        }
      } else if (item.type === 'form-element') {
        // Handle rearranging existing elements within or between sections
        const draggedElement = item.element;

        // Only allow field elements to be moved into sections, not sections
        if (draggedElement._meta._type === 'field') {
          contextMoveElement(draggedElement.id, targetIndex, sectionId);
        }
      }

      // Hide drop zones after drop
      if (setShowDropZones) {
        setShowDropZones(false);
      }
    };

    // Set up drop zone for the section (only for empty sections)
    const [{ isOver: isSectionOver }, sectionDrop] = useDrop({
      accept: ['palette-item', 'form-element'],
      canDrop: (item: any) => {
        // Don't allow sections to be dropped into sections
        if (item.type === 'palette-item' && item.item.type === 'section') {
          return false;
        }
        if (item.type === 'form-element' && item.element._meta._type === 'section') {
          return false;
        }
        return true;
      },
      drop: (item: any, monitor) => {
        // Only handle drops if no more specific drop zone handled it
        if (!monitor.didDrop() && sectionData.fields.length === 0) {
          if (item.type === 'palette-item' && item.item.type !== 'section') {
            // Only allow fields to be dropped into sections, not other sections
            const newField = createDefaultField(item.item.fieldType!);
            contextAddElement(newField, undefined, element.id);
          } else if (item.type === 'form-element' && item.element._meta._type === 'field') {
            // Move existing field to this section
            contextMoveElement(item.element.id, sectionData.fields.length, element.id);
          }
        }
        return { dropped: true, sectionDrop: true };
      },
      hover: (item, monitor) => {
        // Hide main drop zones when hovering over section
        if (setShowDropZones) {
          setShowDropZones(false);
        }
        return { sectionHover: true };
      },
      collect: (monitor) => ({
        isOver: monitor.isOver() && monitor.canDrop()
      })
    });

    return (
      <div
        ref={(node) => {
          drag(node);
          // Don't attach sectionDrop to the main div to avoid conflicts
        }}
        style={{
          border: isSelected ? '2px solid #007bff' : '1px solid #ddd',
          borderRadius: '4px',
          padding: '16px',
          margin: '8px 0',
          backgroundColor: '#f8f9fa',
          position: 'relative',
          cursor: isDragging ? 'grabbing' : 'grab',
          opacity: isDragging ? 0.5 : 1
        }}
      >
        <button
          onClick={handleEdit}
          style={{
            position: 'absolute',
            top: '8px',
            right: '32px',
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            width: '24px',
            height: '24px',
            cursor: 'pointer',
            fontSize: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10
          }}
        >
          ✎
        </button>

        <button
          onClick={handleDelete}
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            background: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '50%',
            width: '24px',
            height: '24px',
            cursor: 'pointer',
            fontSize: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10
          }}
        >
          ×
        </button>

        <h4 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          {sectionData.label}
        </h4>
        {sectionData.description && (
          <p style={{ margin: '0 0 12px 0', fontSize: '14px', color: '#666' }}>
            {sectionData.description}
          </p>
        )}

        <div
          ref={sectionDrop}
          style={{
            minHeight: '40px',
            padding: '8px',
            border: isSectionOver ? '2px dashed #007bff' : '1px dashed #ccc',
            borderRadius: '4px',
            backgroundColor: isSectionOver ? '#e3f2fd' : 'transparent'
          }}
        >
          {sectionData.fields.length === 0 ? (
            <div style={{ textAlign: 'center', color: '#999', fontSize: '14px' }}>
              Drop fields here
            </div>
          ) : (
            <>
              {/* Drop zone at the beginning of section */}
              <SectionDropZone
                onDrop={(item) => handleSectionFieldDrop(item, element.id, 0)}
                index={0}
                isActive={showDropZones || contextState.isDragging}
                draggedElementIndex={draggedElementIndex}
                sectionId={element.id}
              />

              {sectionData.fields.map((field, fieldIndex) => (
                <React.Fragment key={field.id}>
                  <GridElement
                    element={field}
                    index={fieldIndex}
                    onDelete={(id) => contextDeleteElement(id)}
                    onSelect={(element, type) => contextSelectElement(element, 'field')}
                    isSelected={contextState.selectedElement?.id === field.id}
                    showDropZones={showDropZones}
                    draggedElementIndex={draggedElementIndex}
                    setShowDropZones={setShowDropZones}
                  />

                  {/* Drop zone after each field */}
                  <SectionDropZone
                    onDrop={(item) => handleSectionFieldDrop(item, element.id, fieldIndex + 1)}
                    index={fieldIndex + 1}
                    isActive={showDropZones || contextState.isDragging}
                    draggedElementIndex={draggedElementIndex}
                    sectionId={element.id}
                  />
                </React.Fragment>
              ))}
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      ref={drag}
      style={{
        border: isSelected ? '2px solid #007bff' : '1px solid #ddd',
        borderRadius: '4px',
        padding: '8px',
        margin: '8px 0',
        backgroundColor: '#fff',
        position: 'relative',
        cursor: isDragging ? 'grabbing' : 'grab',
        opacity: isDragging ? 0.5 : 1
      }}
    >
      <button
        onClick={handleEdit}
        style={{
          position: 'absolute',
          top: '8px',
          right: '32px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '50%',
          width: '20px',
          height: '20px',
          cursor: 'pointer',
          fontSize: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10
        }}
      >
        ✎
      </button>

      <button
        onClick={handleDelete}
        style={{
          position: 'absolute',
          top: '8px',
          right: '8px',
          background: '#dc3545',
          color: 'white',
          border: 'none',
          borderRadius: '50%',
          width: '20px',
          height: '20px',
          cursor: 'pointer',
          fontSize: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10
        }}
      >
        ×
      </button>

      <FormField
        key={`${fieldData.id}-${fieldData.options.length}-${fieldData.constraints.allow_multiple}-${fieldData.constraints.min_count}`}
        field={fieldData}
        value={
          fieldData.type === 'checkbox' && fieldData.options.length > 0 ? [] :
          fieldData.constraints.allow_multiple ?
            Array(Math.max(fieldData.constraints.min_count || 1, 1)).fill("") :
            fieldData.type === 'checkbox' ? false : ""
        }
        onChange={() => {}}
        onMultipleValuesChange={() => {}}
        onAddMultipleValue={() => {}}
        onRemoveMultipleValue={() => {}}
      />
    </div>
  );
};

// Main grid component
const FormBuilderGrid: React.FC = () => {
  const { state, addElement, deleteElement, moveElement, selectElement, getFormJson } = useFormBuilder();
  const [showDropZones, setShowDropZones] = useState(false);
  const [draggedElementIndex, setDraggedElementIndex] = useState<number | undefined>(undefined);

  const handleDrop = (item: any, targetIndex: number) => {
    if (item.type === 'palette-item') {
      const paletteItem = item.item;
      let newElement;

      if (paletteItem.type === 'section') {
        newElement = createDefaultSection();
        newElement.ordinal = targetIndex;
      } else {
        newElement = createDefaultField(paletteItem.fieldType!);
        newElement.ordinal = targetIndex;
      }

      addElement(newElement, targetIndex);
    } else if (item.type === 'form-element') {
      // Handle rearranging existing elements
      const draggedElement = item.element;
      const sourceIndex = item.sourceIndex;

      if (sourceIndex !== targetIndex) {
        // Use moveElement for proper rearranging
        moveElement(draggedElement.id, targetIndex);
      }
    }
    setShowDropZones(false);
    setDraggedElementIndex(undefined);
  };

  const handleSave = async () => {
    console.log('Form JSON:', getFormJson());
    const response = await axios.post('/dev/form/save', {
        form:getFormJson()
    },{
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    });
    console.log('Form saved:', response.data);
  };

  // Set up drop zone for the entire grid
  const [{ isOver }, drop] = useDrop({
    accept: ['palette-item', 'form-element'],
    drop: (item, monitor) => {
      // Only handle the drop if it wasn't handled by a more specific drop zone
      if (!monitor.didDrop()) {
        handleDrop(item, state.form.elements.length);
      }
      // Reset dragged element tracking
      setDraggedElementIndex(undefined);
      setShowDropZones(false);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    }),
    hover: (item: any, monitor) => {
      setShowDropZones(true);

      // Track which element is being dragged
      if (item.type === 'form-element' && item.sourceIndex !== undefined) {
        setDraggedElementIndex(item.sourceIndex);
      } else {
        setDraggedElementIndex(undefined);
      }
    }
  });

  return (
    <div
      id="form-builder-grid"
      ref={drop}
      style={{
        flex: 1,
        backgroundColor: '#fff',
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '16px',
        minHeight: '600px',
        backgroundImage: `
          radial-gradient(circle, #ddd 1px, transparent 1px)
        `,
        backgroundSize: '20px 20px',
        position: 'relative',
        overflow: 'auto'
      }}
    >
      <div style={{
        backgroundColor: '#fff',
        padding: '16px',
        borderRadius: '4px',
        minHeight: '100%'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
          borderBottom: '1px solid #eee',
          paddingBottom: '8px'
        }}>
          <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>
            Form Builder
          </h3>
          <div>
            <button
              onClick={() => selectElement(null, 'form')}
              style={{
                padding: '6px 12px',
                marginRight: '8px',
                backgroundColor: state.selectedElementType === 'form' ? '#007bff' : '#f8f9fa',
                color: state.selectedElementType === 'form' ? 'white' : '#333',
                border: '1px solid #ddd',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              Form Settings
            </button>
          </div>
        </div>

        {/* Drop zone at the beginning */}
        <DropZone
          onDrop={(item) => handleDrop(item, 0)}
          index={0}
          isActive={showDropZones || state.isDragging}
          draggedElementIndex={draggedElementIndex}
        />

        {/* Render form elements */}
        {state.form.elements.map((element, index) => (
          <React.Fragment key={element.id}>
            <GridElement
              element={element}
              index={index}
              onDelete={deleteElement}
              onSelect={selectElement}
              isSelected={state.selectedElement?.id === element.id}
              showDropZones={showDropZones}
              draggedElementIndex={draggedElementIndex}
              setShowDropZones={setShowDropZones}
            />

            {/* Drop zone after each element */}
            <DropZone
              onDrop={(item) => handleDrop(item, index + 1)}
              index={index + 1}
              isActive={showDropZones || state.isDragging}
              draggedElementIndex={draggedElementIndex}
            />
          </React.Fragment>
        ))}

        {/* Empty state */}
        {state.form.elements.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#999',
            fontSize: '16px'
          }}>
            Drag fields from the palette to start building your form
          </div>
        )}

        {/* Save button */}
        <div style={{
          marginTop: '32px',
          paddingTop: '16px',
          borderTop: '1px solid #eee',
          textAlign: 'center'
        }}>
          <button
            onClick={handleSave}
            style={{
              padding: '12px 24px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px',
              fontWeight: 'bold'
            }}
          >
            Save Form
          </button>
        </div>
      </div>
    </div>
  );
};

export default FormBuilderGrid;
