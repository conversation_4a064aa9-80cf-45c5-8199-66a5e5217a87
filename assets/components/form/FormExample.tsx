import * as React from 'react';
import { useRef, useEffect } from 'react';
import Form, { FormInstance } from './Form';

/**
 * Example component demonstrating how to use the Form component's event system
 */
const FormExample: React.FC = () => {
  const formRef = useRef<FormInstance>(null);

  useEffect(() => {
    if (formRef.current) {
      const form = formRef.current;

      // Example: beforeReady event - runs just as the form starts to load
      form.beforeReady((formInstance) => {
        console.log('Form is about to load:', formInstance.formId);
        // You can perform any setup here before the form loads
      });

      // Example: onReady event - runs when form has completed loading and rendering
      form.onReady((formInstance) => {
        console.log('Form is ready:', formInstance.formId);
        console.log('Initial form values:', formInstance.formValues);
        // You can access the fully loaded form here
      });

      // Example: onSubmit event - runs before form submission
      form.onSubmit((formInstance, formData) => {
        console.log('Form about to submit with data:', formData);
        
        // You can modify the form data here
        if (formData.email) {
          formData.email = formData.email.toLowerCase();
        }
        
        // Add a timestamp
        formData.submitted_at = new Date().toISOString();
        
        // Validate required fields
        if (!formData.name || !formData.email) {
          alert('Name and email are required!');
          return false; // Cancel submission
        }
        
        return true; // Allow submission
      });

      // Example: onChange event for specific field
      form.onChange('email', (formInstance, field, prevValue, newValue) => {
        console.log(`Email field changed from "${prevValue}" to "${newValue}"`);
        
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (newValue && !emailRegex.test(newValue)) {
          alert('Please enter a valid email address');
          return false; // Cancel the change
        }
        
        return true; // Accept the change
      });

      // Example: onChange event for another field
      form.onChange('age', (formInstance, field, prevValue, newValue) => {
        console.log(`Age field changed from "${prevValue}" to "${newValue}"`);
        
        // Validate age range
        const age = parseInt(newValue);
        if (isNaN(age) || age < 0 || age > 120) {
          alert('Please enter a valid age between 0 and 120');
          return false; // Cancel the change
        }
        
        return true; // Accept the change
      });

      // Example: onSuccess event - runs when form submission succeeds
      form.onSuccess((formInstance, responseData) => {
        console.log('Form submitted successfully!', responseData);
        alert('Form submitted successfully!');
        
        // You could redirect, show a success message, etc.
        // window.location.href = '/success';
      });

      // Example: onFailure event - runs when form submission fails
      form.onFailure((formInstance, errorData) => {
        console.error('Form submission failed:', errorData);
        alert('Form submission failed. Please try again.');
        
        // You could show specific error messages, retry logic, etc.
      });
    }
  }, []);

  return (
    <div>
      <h1>Form Example with Events</h1>
      <Form 
        ref={formRef}
        formId="example-form-id"
        containerId="example-container"
      />
    </div>
  );
};

export default FormExample;
