# ClubsterForm JavaScript API

This document describes how to use the ClubsterForm JavaScript API to control form behavior from plain JavaScript in Twig templates.

## Overview

The ClubsterForm API provides a simple JavaScript interface to interact with React Form components without needing to write React code. It automatically scans for form containers and mounts the appropriate React components.

## Basic Usage

### 1. Include the Form Script

In your Twig template, include the form script:

```twig
{% block javascripts %}
    {{ parent() }}
    {{ encore_entry_script_tags('js/form') }}
{% endblock javascripts %}
```

### 2. Get a Form Instance

```javascript
// Get the form API for a specific form ID
const form = ClubsterForm.get('your-form-id');
```

### 3. Register Event Callbacks

```javascript
// Handle form submission
form.onSubmit(function(form, formData) {
    // Your logic here
    return true; // or false to cancel
});
```

## API Reference

### ClubsterForm.get(formId)

Returns a form API object for the specified form ID.

**Parameters:**
- `formId` (string): The ID of the form

**Returns:** Form API object with event methods

### Event Methods

#### form.onSubmit(callback)

Register a callback that runs before form submission.

**Callback Signature:**
```javascript
function(form, formData) {
    // form: { formId, formData, formValues }
    // formData: Object containing all form field values
    // Return true to allow submission, false to cancel
}
```

**Example:**
```javascript
form.onSubmit(function(form, formData, setFormValues) {
    // Validate data
    if (!formData.email) {
        alert('Email is required');
        return false;
    }
    
    // Modify data
    formData.email = formData.email.toLowerCase();
    formData.submitted_at = new Date().toISOString();
    
    // set the modified data that will be submitted
    setFormValues(formData);
    
    return true;
});
```

#### form.onChange(fieldId, callback)

Register a callback that runs when a specific field changes.

**Parameters:**
- `fieldId` (string): The ID of the field to monitor

**Callback Signature:**
```javascript
function(form, field, prevValue, newValue) {
    // form: { formId, formData, formValues }
    // field: Field configuration object
    // prevValue: Previous field value
    // newValue: New field value
    // Return true to accept change, false to cancel
}
```

**Example:**
```javascript
form.onChange('email', function(form, field, prevValue, newValue) {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (newValue && !emailRegex.test(newValue)) {
        alert('Invalid email format');
        return false; // Cancel the change
    }
    return true; // Accept the change
});
```

#### form.beforeReady(callback)

Register a callback that runs just as the form starts to load.

**Callback Signature:**
```javascript
function(form) {
    // form: { formId, formData, formValues }
}
```

**Example:**
```javascript
form.beforeReady(function(form) {
    console.log('Form loading:', form.formId);
    // Show loading indicator
    document.getElementById('loading').style.display = 'block';
});
```

#### form.onReady(callback)

Register a callback that runs when the form is fully loaded and ready.

**Callback Signature:**
```javascript
function(form) {
    // form: { formId, formData, formValues }
}
```

**Example:**
```javascript
form.onReady(function(form) {
    console.log('Form ready:', form.formId);
    console.log('Initial values:', form.formValues);
    // Hide loading indicator
    document.getElementById('loading').style.display = 'none';
});
```

#### form.onSuccess(callback)

Register a callback that runs when form submission succeeds.

**Callback Signature:**
```javascript
function(form, responseData) {
    // form: { formId, formData, formValues }
    // responseData: Server response data
}
```

**Example:**
```javascript
form.onSuccess(function(form, responseData) {
    alert('Form submitted successfully!');
    console.log('Server response:', responseData);
    // Redirect or show success message
    window.location.href = '/success';
});
```

#### form.onFailure(callback)

Register a callback that runs when form submission fails.

**Callback Signature:**
```javascript
function(form, errorData) {
    // form: { formId, formData, formValues }
    // errorData: Error information
}
```

**Example:**
```javascript
form.onFailure(function(form, errorData) {
    console.error('Submission failed:', errorData);
    alert('Submission failed. Please try again.');
    
    // Show specific error message if available
    if (errorData.response && errorData.response.data) {
        alert('Error: ' + errorData.response.data.message);
    }
});
```

## Complete Example

```javascript
document.addEventListener('DOMContentLoaded', function() {
    const form = ClubsterForm.get('contact-form');
    
    // Form lifecycle
    form.beforeReady(function(form) {
        console.log('Loading form...');
    });
    
    form.onReady(function(form) {
        console.log('Form ready with values:', form.formValues);
    });
    
    // Field validation
    form.onChange('email', function(form, field, prevValue, newValue) {
        if (newValue && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newValue)) {
            alert('Invalid email format');
            return false;
        }
        return true;
    });
    
    form.onChange('phone', function(form, field, prevValue, newValue) {
        // Auto-format phone number
        if (newValue) {
            const formatted = newValue.replace(/\D/g, '').replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            if (formatted !== newValue) {
                // Note: This won't work as expected since we can't modify the value
                // This is just an example of validation
                console.log('Phone formatted:', formatted);
            }
        }
        return true;
    });
    
    // Form submission
    form.onSubmit(function(form, formData) {
        // Add metadata
        formData.submitted_at = new Date().toISOString();
        formData.user_agent = navigator.userAgent;
        
        // Validate required fields
        if (!formData.name || !formData.email) {
            alert('Name and email are required');
            return false;
        }
        
        return true;
    });
    
    // Handle results
    form.onSuccess(function(form, response) {
        alert('Thank you! Your message has been sent.');
        // Clear form or redirect
    });
    
    form.onFailure(function(form, error) {
        alert('Sorry, there was an error sending your message. Please try again.');
    });
});
```

## Advanced Usage

### Multiple Event Handlers

You can register multiple handlers for the same event:

```javascript
form.onSubmit(function(form, formData, setFormValues) {
    // First handler - validation
    if (!formData.email) return false;
    return true;
});

form.onSubmit(function(form, formData, setFormValues) {
    // Second handler - data processing
    formData.email = formData.email.toLowerCase();
    setFormValues(formData);
    return true;
});
```

### Dynamic Forms

If you add forms to the page dynamically, call `ClubsterForm.refresh()` to scan for new forms:

```javascript
// After adding new form HTML to the page
ClubsterForm.refresh();
```

## Form Submission

Forms automatically submit to `/dev/form/submit` via POST with this payload:

```json
{
  "form": {
    "id": "your-form-id"
  },
  "data": {
    "field1": "value1",
    "field2": "value2"
  },
  "_meta": {
    "submitted_at": "2023-09-12T12:34:56Z",
    "submitted_from": "web"
  }
}
```
