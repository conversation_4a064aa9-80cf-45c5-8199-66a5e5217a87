import * as React from 'react';
import { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import axios from 'axios';
import <PERSON>Field from './FormField';
import FormSection from './FormSection';

interface FormProps {
  formId: string;
  containerId?: string;
  recordRecallCode?: string;
  loadRecord?: boolean;
}

// Event callback types
type OnSubmitCallback = (form: FormInstance, formData: Record<string, any>, setFormValues: (values: Record<string, any>) => void) => boolean;
type OnChangeCallback = (form: FormInstance, field: FormFieldData, prevValue: any, newValue: any) => boolean;
type BeforeReadyCallback = (form: FormInstance) => void;
type OnReadyCallback = (form: FormInstance) => void;
type OnSuccessCallback = (form: FormInstance, responseData: any) => void;
type OnFailureCallback = (form: FormInstance, responseData: any) => void;

// Form instance interface for event callbacks
interface FormInstance {
  formId: string;
  containerId: string;
  recordRecallCode: string;
  loadRecord: boolean;
  formData: FormData | null;
  formValues: Record<string, any>;
  onSubmit: (callback: OnSubmitCallback) => void;
  onChange: (fieldId: string, callback: OnChangeCallback) => void;
  beforeReady: (callback: BeforeReadyCallback) => void;
  onReady: (callback: OnReadyCallback) => void;
  onSuccess: (callback: OnSuccessCallback) => void;
  onFailure: (callback: OnFailureCallback) => void;
}

export interface FormData {
  id: string;
  name: string;
  description: string;
  elements: Array<FormElement>;
  actions: any[];
  _meta: MetaData;
}

export type FormElement = FormFieldData | FormSectionData;

interface FormSectionData {
  id: string;
  label: string;
  description: string;
  ordinal: number;
  fields: FormFieldData[];
  subscriptions?: Record<string, SubscriptionData>;
  _meta: SectionMetaData;
}

interface FormFieldData {
  id: string;
  label: string;
  help_text: string | null;
  type: string;
  required: boolean;
  constraints: {
    min_length: number | null;
    max_length: number | null;
    allow_multiple: boolean;
    min_count: number;
    max_count: number | null;
    accessibility: string;
  };
  ordinal: number;
  options: OptionData[];
  subscriptions: Record<string, SubscriptionData>;
  _meta: FieldMetaData;
}

interface OptionData {
  id: string;
  label: string;
  value: string;
  active: boolean;
  ordinal: number;
  subscriptions: Record<string, SubscriptionData>;
  _meta: MetaData;
}

interface SubscriptionData {
  id: string;
  action: SubscriptionAction[];
  producer: string;
  _meta: MetaData;
}

interface SubscriptionAction {
  _listen?: Record<string, any>;
  _action: Record<string, any>;
  "!important"?: boolean;
  "!default"?: boolean;
}

interface MetaData {
  created_at: string;
  created_by: string;
  updated_at: string | null;
  updated_by: string | null;
  deleted_at: string | null;
}

interface FieldMetaData extends MetaData {
  _type: string;
  section_id: string | null;
}

interface SectionMetaData extends MetaData {
  _type: string;
}

const Form = forwardRef<FormInstance, FormProps>(({ formId, containerId, recordRecallCode, loadRecord }, ref) => {
  const [form, setForm] = useState<FormData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [visibleElements, setVisibleElements] = useState<Record<string, boolean>>({});
  const [elementAttributes, setElementAttributes] = useState<Record<string, Record<string, any>>>({});

  // Event callback storage
  const onSubmitCallbacks = useRef<OnSubmitCallback[]>([]);
  const onChangeCallbacks = useRef<Record<string, OnChangeCallback[]>>({});
  const beforeReadyCallbacks = useRef<BeforeReadyCallback[]>([]);
  const onReadyCallbacks = useRef<OnReadyCallback[]>([]);
  const onSuccessCallbacks = useRef<OnSuccessCallback[]>([]);
  const onFailureCallbacks = useRef<OnFailureCallback[]>([]);

  // Create form instance for callbacks
  const formInstance: FormInstance = {
    formId,
    containerId: containerId || `form-${formId}`,
    recordRecallCode: recordRecallCode || '',
    loadRecord: loadRecord || false,
    formData: form,
    formValues,
    onSubmit: (callback: OnSubmitCallback) => {
      onSubmitCallbacks.current.push(callback);
    },
    onChange: (fieldId: string, callback: OnChangeCallback) => {
      if (!onChangeCallbacks.current[fieldId]) {
        onChangeCallbacks.current[fieldId] = [];
      }
      onChangeCallbacks.current[fieldId].push(callback);
    },
    beforeReady: (callback: BeforeReadyCallback) => {
      beforeReadyCallbacks.current.push(callback);
    },
    onReady: (callback: OnReadyCallback) => {
      onReadyCallbacks.current.push(callback);
    },
    onSuccess: (callback: OnSuccessCallback) => {
      onSuccessCallbacks.current.push(callback);
    },
    onFailure: (callback: OnFailureCallback) => {
      onFailureCallbacks.current.push(callback);
    }
  };

  // Expose form instance via ref
  useImperativeHandle(ref, () => formInstance, [formInstance]);

  useEffect(() => {
    const loadForm = async () => {
      try {
        setLoading(true);

        // Trigger beforeReady callbacks
        beforeReadyCallbacks.current.forEach(callback => {
          try {
            callback(formInstance);
          } catch (error) {
            console.error('Error in beforeReady callback:', error);
          }
        });

        let formLoadEndpoint = `/dev/form/load/?formId=${formId}`;
        if (recordRecallCode) {
          formLoadEndpoint += `&recordRecallCode=${recordRecallCode}`;

          if (loadRecord) {
            formLoadEndpoint += `&loadRecord=${loadRecord}`;
          }
        }

        const response = await axios.get(formLoadEndpoint);
        setForm(response.data);

        // Initialize visible elements and attributes
        const initialVisibility: Record<string, boolean> = {};
        const initialAttributes: Record<string, Record<string, any>> = {};

        response.data.elements.forEach((element: FormElement) => {
          // Handle section visibility
          if (element._meta._type === 'section') {
            const section = element as FormSectionData;
            initialVisibility[section.id] = true;
            initialAttributes[section.id] = { disabled: false, readonly: false };

            // Initialize fields within section
            section.fields.forEach((field: FormFieldData) => {
              initialVisibility[field.id] = true;
              initialAttributes[field.id] = { disabled: false, readonly: false };

              // Check field subscriptions
              if (field.subscriptions) {
                Object.values(field.subscriptions).forEach((sub: SubscriptionData) => {
                  // Handle new subscription format
                  if (Array.isArray(sub.action)) {
                    // Apply default actions during initialization
                    const defaultActions = sub.action.filter(actionItem => actionItem["!default"]);
                    defaultActions.forEach((actionItem: SubscriptionAction) => {
                      if (actionItem._action.display === false) {
                        initialVisibility[field.id] = false;
                      }
                    });
                  } else {
                    // Handle legacy format
                    if (sub.action && (sub.action as any).default === 'hidden') {
                      initialVisibility[field.id] = false;
                    }
                  }
                });
              }
            });

            // Check section subscriptions
            if (section.subscriptions) {
              Object.values(section.subscriptions).forEach((sub: SubscriptionData) => {
                // Handle new subscription format
                if (Array.isArray(sub.action)) {
                  // Apply default actions during initialization
                  const defaultActions = sub.action.filter(actionItem => actionItem["!default"]);
                  defaultActions.forEach((actionItem: SubscriptionAction) => {
                    if (actionItem._action.display === false) {
                      initialVisibility[section.id] = false;
                    }
                  });
                } else {
                  // Handle legacy format
                  if (sub.action && (sub.action as any).default === 'hidden') {
                    initialVisibility[section.id] = false;
                  }
                }
              });
            }
          } else {
            // Handle field visibility
            const field = element as FormFieldData;
            initialVisibility[field.id] = true;
            initialAttributes[field.id] = { disabled: false, readonly: false };

            if (field.subscriptions) {
              Object.values(field.subscriptions).forEach((sub: SubscriptionData) => {
                // Handle new subscription format
                if (Array.isArray(sub.action)) {
                  // Apply default actions during initialization
                  const defaultActions = sub.action.filter(actionItem => actionItem["!default"]);
                  defaultActions.forEach((actionItem: SubscriptionAction) => {
                    if (actionItem._action.display === false) {
                      initialVisibility[field.id] = false;
                    }
                  });
                } else {
                  // Handle legacy format
                  if (sub.action && (sub.action as any).default === 'hidden') {
                    initialVisibility[field.id] = false;
                  }
                }
              });
            }
          }
        });

        setVisibleElements(initialVisibility);
        setElementAttributes(initialAttributes);

        // Trigger onReady callbacks after form is loaded and initialized
        setTimeout(() => {
          onReadyCallbacks.current.forEach(callback => {
            try {
              callback(formInstance);
            } catch (error) {
              console.error('Error in onReady callback:', error);
            }
          });
        }, 0);

      } catch (err) {
        setError('Failed to load form data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (formId) {
      loadForm();
    }
  }, [formId]);

  const handleFieldChange = (fieldId: string, value: any) => {
    const prevValue = formValues[fieldId];

    // Trigger onChange callbacks for this field
    const fieldCallbacks = onChangeCallbacks.current[fieldId] || [];
    let shouldProceed = true;

    if (fieldCallbacks.length > 0) {
      const field = form?.elements.find(e => e.id === fieldId) as FormFieldData;
      if (field) {
        for (const callback of fieldCallbacks) {
          try {
            const result = callback(formInstance, field, prevValue, value);
            if (result === false) {
              shouldProceed = false;
              break;
            }
          } catch (error) {
            console.error('Error in onChange callback:', error);
          }
        }
      }
    }

    if (!shouldProceed) {
      return; // Cancel the change
    }

    setFormValues(prev => ({
      ...prev,
      [fieldId]: value
    }));

    // Handle element visibility and attributes based on subscriptions
    if (form) {
      const updatedVisibility = { ...visibleElements };
      const updatedAttributes = { ...elementAttributes };

      // Process all elements to find subscriptions
      form.elements.forEach((element: FormElement) => {
        if (element._meta._type === 'section') {
          const section = element as FormSectionData;
          // Process fields within section
          section.fields.forEach((field: FormFieldData) => {
            processSubscriptions(field, fieldId, value, updatedVisibility, updatedAttributes);

            // Process field option subscriptions
            field.options.forEach(option => {
              processOptionSubscriptions(option, fieldId, value, updatedVisibility, updatedAttributes);
            });
          });

          // Process section subscriptions
          if (section.subscriptions) {
            processSectionSubscriptions(section, fieldId, value, updatedVisibility, updatedAttributes);
          }
        } else {
          // Process field subscriptions
          const field = element as FormFieldData;
          processSubscriptions(field, fieldId, value, updatedVisibility, updatedAttributes);

          // Process field option subscriptions
          field.options.forEach(option => {
            processOptionSubscriptions(option, fieldId, value, updatedVisibility, updatedAttributes);
          });
        }
      });

      // Check if visibility changed and trigger display-based subscriptions
      const visibilityChanged = Object.keys(updatedVisibility).some(
        key => updatedVisibility[key] !== visibleElements[key]
      );

      if (visibilityChanged) {
        // Process display-based subscriptions
        processDisplaySubscriptions(updatedVisibility, updatedAttributes);
      }

      setVisibleElements(updatedVisibility);
      setElementAttributes(updatedAttributes);
    }
  };

  const processDisplaySubscriptions = (
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    if (!form) return;

    // Process all elements to find display-based subscriptions
    form.elements.forEach((element: FormElement) => {
      if (element._meta._type === 'section') {
        const section = element as FormSectionData;
        // Process fields within section
        section.fields.forEach((field: FormFieldData) => {
          processDisplaySubscriptionsForElement(field, visibilityState, attributesState);

          // Process field option subscriptions
          field.options.forEach(option => {
            processDisplaySubscriptionsForOption(option, visibilityState, attributesState);
          });
        });

        // Process section subscriptions
        if (section.subscriptions) {
          processDisplaySubscriptionsForSection(section, visibilityState, attributesState);
        }
      } else {
        // Process field subscriptions
        const field = element as FormFieldData;
        processDisplaySubscriptionsForElement(field, visibilityState, attributesState);

        // Process field option subscriptions
        field.options.forEach(option => {
          processDisplaySubscriptionsForOption(option, visibilityState, attributesState);
        });
      }
    });
  };

  const processDisplaySubscriptionsForElement = (
    element: FormFieldData,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    if (element.subscriptions) {
      Object.values(element.subscriptions).forEach((sub: SubscriptionData) => {
        if (Array.isArray(sub.action)) {
          sub.action.forEach((actionItem: SubscriptionAction) => {
            if (actionItem._listen?.display !== undefined) {
              const producerVisible = visibilityState[sub.producer];
              if (producerVisible === actionItem._listen.display) {
                applyAction(actionItem._action, element.id, visibilityState, attributesState);
              }
            }
          });
        }
      });
    }
  };

  const processDisplaySubscriptionsForSection = (
    section: FormSectionData,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    if (section.subscriptions) {
      Object.values(section.subscriptions).forEach((sub: SubscriptionData) => {
        if (Array.isArray(sub.action)) {
          sub.action.forEach((actionItem: SubscriptionAction) => {
            if (actionItem._listen?.display !== undefined) {
              const producerVisible = visibilityState[sub.producer];
              if (producerVisible === actionItem._listen.display) {
                applySectionAction(actionItem._action, section, visibilityState, attributesState);
              }
            }
          });
        }
      });
    }
  };

  const processDisplaySubscriptionsForOption = (
    option: OptionData,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    if (option.subscriptions) {
      Object.values(option.subscriptions).forEach((sub: SubscriptionData) => {
        if (Array.isArray(sub.action)) {
          sub.action.forEach((actionItem: SubscriptionAction) => {
            if (actionItem._listen?.display !== undefined) {
              const producerVisible = visibilityState[sub.producer];
              if (producerVisible === actionItem._listen.display) {
                applyOptionAction(actionItem._action, option, visibilityState, attributesState);
              }
            }
          });
        }
      });
    }
  };

  const evaluateListenCondition = (
    listenCondition: Record<string, any>,
    value: any,
    producerFieldId: string,
    visibilityState: Record<string, boolean>
  ): boolean => {
    // Handle value-based conditions
    if (listenCondition.value !== undefined) {
      return value === listenCondition.value;
    }

    // Handle checked state for checkboxes
    if (listenCondition.checked !== undefined) {
      return Boolean(value) === listenCondition.checked;
    }

    // Handle display state listening
    if (listenCondition.display !== undefined) {
      return visibilityState[producerFieldId] === listenCondition.display;
    }

    // Handle HTML attribute conditions (for future use)
    for (const [key, expectedValue] of Object.entries(listenCondition)) {
      if (key.startsWith('[data-') && key.endsWith(']')) {
        // This would require DOM access to check attributes
        // For now, we'll skip this but the structure is ready
        continue;
      }
    }

    return false;
  };

  const applyAction = (
    action: Record<string, any>,
    elementId: string,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    // Handle display action
    if (action.display !== undefined) {
      visibilityState[elementId] = action.display;
    }

    // Handle attribute actions
    if (action.disabled !== undefined) {
      attributesState[elementId] = {
        ...attributesState[elementId],
        disabled: action.disabled
      };
    }

    if (action.readonly !== undefined) {
      attributesState[elementId] = {
        ...attributesState[elementId],
        readonly: action.readonly
      };
    }

    // Handle HTML attribute actions (for future use)
    for (const [key, value] of Object.entries(action)) {
      if (key.startsWith('[data-') && key.endsWith(']')) {
        const attributeName = key.slice(1, -1); // Remove brackets
        attributesState[elementId] = {
          ...attributesState[elementId],
          [attributeName]: value
        };
      }
    }
  };

  const applySectionAction = (
    action: Record<string, any>,
    section: FormSectionData,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    // Handle display action for section
    if (action.display !== undefined) {
      visibilityState[section.id] = action.display;
    }

    // Handle attribute actions for section and propagate to fields
    if (action.disabled !== undefined) {
      attributesState[section.id] = {
        ...attributesState[section.id],
        disabled: action.disabled
      };

      // Apply to all fields in the section
      section.fields.forEach(field => {
        attributesState[field.id] = {
          ...attributesState[field.id],
          disabled: action.disabled
        };
      });
    }

    if (action.readonly !== undefined) {
      attributesState[section.id] = {
        ...attributesState[section.id],
        readonly: action.readonly
      };

      // Apply to all fields in the section
      section.fields.forEach(field => {
        attributesState[field.id] = {
          ...attributesState[field.id],
          readonly: action.readonly
        };
      });
    }

    // Handle HTML attribute actions for section
    for (const [key, value] of Object.entries(action)) {
      if (key.startsWith('[data-') && key.endsWith(']')) {
        const attributeName = key.slice(1, -1); // Remove brackets
        attributesState[section.id] = {
          ...attributesState[section.id],
          [attributeName]: value
        };
      }
    }
  };

  const processOptionSubscriptions = (
    option: OptionData,
    changedFieldId: string,
    value: any,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    if (option.subscriptions) {
      Object.values(option.subscriptions).forEach((sub: SubscriptionData) => {
        if (sub.producer === changedFieldId) {
          // Handle new subscription format
          if (Array.isArray(sub.action)) {
            processSubscriptionActionsForOption(sub.action, value, changedFieldId, option, visibilityState, attributesState);
          } else {
            // Handle legacy format for backward compatibility
            const legacyAction = sub.action as any;
            const changedField = form?.elements.find(e => e.id === changedFieldId);
            if (changedField && changedField._meta._type === 'field') {
              const fieldData = changedField as FormFieldData;
              if (fieldData.type === 'checkbox') {
                if (value && legacyAction.checked === 'display') {
                  // For options, we might want to modify the option's active state
                  // This would require a different state management approach
                } else if (!value && legacyAction.default === 'hidden') {
                  // Similar handling for hiding options
                }
              }
            }
          }
        }
      });
    }
  };

  const applyOptionAction = (
    action: Record<string, any>,
    option: OptionData,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    // For options, we might need to handle visibility differently
    // since options don't have their own visibility state in the current implementation
    // This could be extended to manage option visibility/availability

    // Handle display action for option (would need additional state management)
    if (action.display !== undefined) {
      // Could set option.active = action.display, but this would require
      // modifying the form data directly, which might not be ideal
      // For now, we'll leave this as a placeholder for future implementation
    }

    // Handle HTML attribute actions for options
    for (const [key, value] of Object.entries(action)) {
      if (key.startsWith('[data-') && key.endsWith(']')) {
        const attributeName = key.slice(1, -1); // Remove brackets
        attributesState[option.id] = {
          ...attributesState[option.id],
          [attributeName]: value
        };
      }
    }
  };

  const processSubscriptionActions = (
    actions: SubscriptionAction[],
    value: any,
    changedFieldId: string,
    elementId: string,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    let matchedAction = false;
    const defaultActions: SubscriptionAction[] = [];
    const importantActions: SubscriptionAction[] = [];
    const normalActions: SubscriptionAction[] = [];

    // Separate actions by priority and type
    actions.forEach((actionItem: SubscriptionAction) => {
      if (actionItem["!default"]) {
        defaultActions.push(actionItem);
      } else if (actionItem["!important"]) {
        importantActions.push(actionItem);
      } else {
        normalActions.push(actionItem);
      }
    });

    // Process important actions first
    importantActions.forEach((actionItem: SubscriptionAction) => {
      if (actionItem._listen && evaluateListenCondition(actionItem._listen, value, changedFieldId, visibilityState)) {
        applyAction(actionItem._action, elementId, visibilityState, attributesState);
        matchedAction = true;
      }
    });

    // Process normal actions if no important action matched
    if (!matchedAction) {
      normalActions.forEach((actionItem: SubscriptionAction) => {
        if (actionItem._listen && evaluateListenCondition(actionItem._listen, value, changedFieldId, visibilityState)) {
          applyAction(actionItem._action, elementId, visibilityState, attributesState);
          matchedAction = true;
        }
      });
    }

    // Apply default actions if no specific condition matched
    if (!matchedAction) {
      defaultActions.forEach((actionItem: SubscriptionAction) => {
        applyAction(actionItem._action, elementId, visibilityState, attributesState);
      });
    }
  };

  const processSubscriptionActionsForSection = (
    actions: SubscriptionAction[],
    value: any,
    changedFieldId: string,
    section: FormSectionData,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    let matchedAction = false;
    const defaultActions: SubscriptionAction[] = [];
    const importantActions: SubscriptionAction[] = [];
    const normalActions: SubscriptionAction[] = [];

    // Separate actions by priority and type
    actions.forEach((actionItem: SubscriptionAction) => {
      if (actionItem["!default"]) {
        defaultActions.push(actionItem);
      } else if (actionItem["!important"]) {
        importantActions.push(actionItem);
      } else {
        normalActions.push(actionItem);
      }
    });

    // Process important actions first
    importantActions.forEach((actionItem: SubscriptionAction) => {
      if (actionItem._listen && evaluateListenCondition(actionItem._listen, value, changedFieldId, visibilityState)) {
        applySectionAction(actionItem._action, section, visibilityState, attributesState);
        matchedAction = true;
      }
    });

    // Process normal actions if no important action matched
    if (!matchedAction) {
      normalActions.forEach((actionItem: SubscriptionAction) => {
        if (actionItem._listen && evaluateListenCondition(actionItem._listen, value, changedFieldId, visibilityState)) {
          applySectionAction(actionItem._action, section, visibilityState, attributesState);
          matchedAction = true;
        }
      });
    }

    // Apply default actions if no specific condition matched
    if (!matchedAction) {
      defaultActions.forEach((actionItem: SubscriptionAction) => {
        applySectionAction(actionItem._action, section, visibilityState, attributesState);
      });
    }
  };

  const processSubscriptionActionsForOption = (
    actions: SubscriptionAction[],
    value: any,
    changedFieldId: string,
    option: OptionData,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    let matchedAction = false;
    const defaultActions: SubscriptionAction[] = [];
    const importantActions: SubscriptionAction[] = [];
    const normalActions: SubscriptionAction[] = [];

    // Separate actions by priority and type
    actions.forEach((actionItem: SubscriptionAction) => {
      if (actionItem["!default"]) {
        defaultActions.push(actionItem);
      } else if (actionItem["!important"]) {
        importantActions.push(actionItem);
      } else {
        normalActions.push(actionItem);
      }
    });

    // Process important actions first
    importantActions.forEach((actionItem: SubscriptionAction) => {
      if (actionItem._listen && evaluateListenCondition(actionItem._listen, value, changedFieldId, visibilityState)) {
        applyOptionAction(actionItem._action, option, visibilityState, attributesState);
        matchedAction = true;
      }
    });

    // Process normal actions if no important action matched
    if (!matchedAction) {
      normalActions.forEach((actionItem: SubscriptionAction) => {
        if (actionItem._listen && evaluateListenCondition(actionItem._listen, value, changedFieldId, visibilityState)) {
          applyOptionAction(actionItem._action, option, visibilityState, attributesState);
          matchedAction = true;
        }
      });
    }

    // Apply default actions if no specific condition matched
    if (!matchedAction) {
      defaultActions.forEach((actionItem: SubscriptionAction) => {
        applyOptionAction(actionItem._action, option, visibilityState, attributesState);
      });
    }
  };

  const processSubscriptions = (
    element: FormFieldData,
    changedFieldId: string,
    value: any,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    if (element.subscriptions) {
      Object.values(element.subscriptions).forEach((sub: SubscriptionData) => {
        if (sub.producer === changedFieldId) {
          // Handle new subscription format
          if (Array.isArray(sub.action)) {
            processSubscriptionActions(sub.action, value, changedFieldId, element.id, visibilityState, attributesState);
          } else {
            // Handle legacy format for backward compatibility
            const legacyAction = sub.action as any;
            const changedField = form?.elements.find(e => e.id === changedFieldId);
            if (changedField && changedField._meta._type === 'field') {
              const fieldData = changedField as FormFieldData;
              if (fieldData.type === 'checkbox') {
                if (value && legacyAction.checked === 'display') {
                  visibilityState[element.id] = true;
                } else if (!value && legacyAction.default === 'hidden') {
                  visibilityState[element.id] = false;
                }
              }
            }

            // Handle other legacy attribute changes
            if (legacyAction.readonly) {
              attributesState[element.id] = {
                ...attributesState[element.id],
                readonly: value ? true : false
              };
            }

            if (legacyAction.disabled) {
              attributesState[element.id] = {
                ...attributesState[element.id],
                disabled: value ? true : false
              };
            }
          }
        }
      });
    }
  };

  const processSectionSubscriptions = (
    section: FormSectionData,
    changedFieldId: string,
    value: any,
    visibilityState: Record<string, boolean>,
    attributesState: Record<string, Record<string, any>>
  ) => {
    if (section.subscriptions) {
      Object.values(section.subscriptions).forEach((sub: SubscriptionData) => {
        if (sub.producer === changedFieldId) {
          // Handle new subscription format
          if (Array.isArray(sub.action)) {
            processSubscriptionActionsForSection(sub.action, value, changedFieldId, section, visibilityState, attributesState);
          } else {
            // Handle legacy format for backward compatibility
            const legacyAction = sub.action as any;
            const changedField = form?.elements.find(e => e.id === changedFieldId);
            if (changedField && changedField._meta._type === 'field') {
              const fieldData = changedField as FormFieldData;
              if (fieldData.type === 'checkbox') {
                if (value && legacyAction.checked === 'display') {
                  visibilityState[section.id] = true;
                } else if (!value && legacyAction.default === 'hidden') {
                  visibilityState[section.id] = false;
                }
              }
            }

            // Handle other legacy attribute changes
            if (legacyAction.readonly) {
              attributesState[section.id] = {
                ...attributesState[section.id],
                readonly: value ? true : false
              };

              // Apply to all fields in the section
              section.fields.forEach(field => {
                attributesState[field.id] = {
                  ...attributesState[field.id],
                  readonly: value ? true : false
                };
              });
            }

            if (legacyAction.disabled) {
              attributesState[section.id] = {
                ...attributesState[section.id],
                disabled: value ? true : false
              };

              // Apply to all fields in the section
              section.fields.forEach(field => {
                attributesState[field.id] = {
                  ...attributesState[field.id],
                  disabled: value ? true : false
                };
              });
            }
          }
        }
      });
    }
  };

  const handleMultipleValuesChange = (fieldId: string, index: number, value: any) => {
    setFormValues(prev => {
      const values = [...(prev[fieldId] || [])];
      values[index] = value;
      return {
        ...prev,
        [fieldId]: values
      };
    });
  };

  const addMultipleValue = (fieldId: string) => {
    setFormValues(prev => {
      const values = [...(prev[fieldId] || []), ''];
      return {
        ...prev,
        [fieldId]: values
      };
    });
  };

  const removeMultipleValue = (fieldId: string, index: number) => {
    setFormValues(prev => {
      const values = [...(prev[fieldId] || [])];
      values.splice(index, 1);
      return {
        ...prev,
        [fieldId]: values
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    let submissionData = { ...formValues };
    let shouldSubmit = true;

    // Trigger onSubmit callbacks
    for (const callback of onSubmitCallbacks.current) {
      try {
        const result = callback(formInstance, submissionData, setFormValues);
        if (result === false) {
          shouldSubmit = false;
          break;
        }
      } catch (error) {
        console.error('Error in onSubmit callback:', error);
      }
    }

    if (!shouldSubmit) {
      return; // Cancel submission
    }

    try {
        var form_data = new FormData();

        for ( var key in formValues ) {
            form_data.append('data['+key+']', formValues[key]);
        }
        form_data.append('form[id]', formId);
        form_data.append('_meta[submitted_at]', new Date().toISOString());
        form_data.append('_meta[submitted_from]', 'web');

      // Submit form data to the server
      const response = await axios.post('/dev/form/submit', form_data, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
      });

      // Trigger onSuccess callbacks
      onSuccessCallbacks.current.forEach(callback => {
        try {
          callback(formInstance, response.data);
        } catch (error) {
          console.error('Error in onSuccess callback:', error);
        }
      });

    } catch (error) {
      console.error('Form submission failed:', error);

      // Trigger onFailure callbacks
      onFailureCallbacks.current.forEach(callback => {
        try {
          callback(formInstance, error);
        } catch (callbackError) {
          console.error('Error in onFailure callback:', callbackError);
        }
      });
    }
  };

  if (loading) return <div>Loading form...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!form) return <div>No form data available</div>;

  // Sort elements by ordinal
  const sortedElements = [...form.elements].sort((a, b) => a.ordinal - b.ordinal);

  return (
    <div className="dynamic-form">
      <h2>{form.name}</h2>
      {form.description && <p>{form.description}</p>}

      <form id={form.id} onSubmit={handleSubmit}>
        {sortedElements.map((element) => {
          if (element._meta._type === 'section') {
            // Render section
            return (
              visibleElements[element.id] && (
                <FormSection
                  key={element.id}
                  section={element as FormSectionData}
                  formValues={formValues}
                  visibleElements={visibleElements}
                  elementAttributes={elementAttributes}
                  onFieldChange={handleFieldChange}
                  onMultipleValuesChange={handleMultipleValuesChange}
                  onAddMultipleValue={addMultipleValue}
                  onRemoveMultipleValue={removeMultipleValue}
                />
              )
            );
          } else {
            // Render standalone field
            return (
              visibleElements[element.id] && (
                <FormField
                  key={element.id}
                  field={element as FormFieldData}
                  value={formValues[element.id]}
                  attributes={elementAttributes[element.id] || {}}
                  onChange={(value) => handleFieldChange(element.id, value)}
                  onMultipleValuesChange={(index, value) => handleMultipleValuesChange(element.id, index, value)}
                  onAddMultipleValue={() => addMultipleValue(element.id)}
                  onRemoveMultipleValue={(index) => removeMultipleValue(element.id, index)}
                />
              )
            );
          }
        })}

        <div className="frm-row">
          <div className="submit-row">
            <div className="input-row-label"></div>
            <div className="input-row-input">
              <button
                type="submit"
                id={`${form.id}_submit_`}
                name={`${form.id}[_submit_]`}
                className="clubster-button"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
});

Form.displayName = 'Form';

export default Form;
export type { FormInstance };
