version: '3'

rpc:
  listen: 'tcp://127.0.0.1:6001'

status:
  address: '0.0.0.0:2114'

server:
  on_init:
    command: 'php init.php'
  command: 'php main.php'
  env:
    - XDEBUG_SESSION: 1


kv:
  local:
      driver: memory
      config: {}

http:
  address: 0.0.0.0:8088
  pool:
    debug: ${ROADRUNNER_DEBUG:-false}
    num_workers: ${ROADRUNNER_NUM_WORKERS:-4}
    command: 'php main.php'
    dynamic_allocator:
      max_workers: 25
      spawn_rate: 10
      idle_timeout: 10s

temporal:
  pool.debug: ${ROADRUNNER_DEBUG:-false}
  address: ${TEMPORAL_ADDRESS}
  namespace: ${TEMPORAL_NAMESPACE}
  activities:
    num_workers: ${TEMPORAL_NUM_WORKERS:-4}
    command: 'php main.php'

logs:
  level: debug
  mode: development
  channels:
    temporal.level: error
