resolver 127.0.0.1;

upstream dynamic {
    server unix:/sock/docker.sock;
}
server {
    listen 80 default_server;
    server_name _;
    root /var/www/public;
    index   index.php;

    gzip on;
    gzip_min_length 100;
    gzip_buffers 4 32k;
    gzip_types text/plain application/javascript text/xml text/css;
    gzip_vary on;

    add_header X-API-Handler $CLUBSTER_API_INTERNAL_DNS;

    large_client_header_buffers 4 16k;

    location / {
        # try to serve file directly, fallback to app.php
        try_files $uri /index.php$is_args$args;
    }

    # PROD
    location ~ ^/index\.php(/|$) {
        fastcgi_pass dynamic;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param HTTPS true;
        # When you are using symlinks to link the document root to the
        # current version of your application, you should pass the real
        # application path instead of the path to the symlink to PHP
        # FPM.
        # Otherwise, PHP's OPcache may not properly detect changes to
        # your PHP files (see https://github.com/zendtech/ZendOptimizerPlus/issues/126
        # for more information).
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_param   REMOTE_ADDR $http_x_real_ip;
        # Prevents URIs that include the front controller. This will 404:
        # http://domain.tld/app.php/some-path
        # Remove the internal directive to allow URIs like this
        internal;
    }

    # return 404 for all other php files not matching the front controller
    # this prevents access to other php files you don't want to be accessible.
    location ~ \.php$ {
        #return 404;
        resolver 127.0.0.1;
        fastcgi_pass dynamic;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        fastcgi_param HTTPS true;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        fastcgi_param   REMOTE_ADDR $http_x_real_ip;
    }


        # Turn off logging for favicons and robots.txt
        location ~ ^/android-chrome-|^/apple-touch-|^/browserconfig.xml$|^/coast-|^/favicon.ico$|^/favicon-|^/firefox_app_|^/manifest.json$|^/manifest.webapp$|^/mstile-|^/open-graph.png$|^/twitter.png$|^/yandex- {
                log_not_found off;
                access_log off;
        }

        location = /robots.txt {
                log_not_found off;
                access_log off;
        }
}
