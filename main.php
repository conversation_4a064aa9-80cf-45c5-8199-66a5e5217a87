<?php

use Clubster\Peregrine\Infrastructure\DependencyInjection\ContainerBuilder;
use Clubster\Peregrine\Infrastructure\Peregrine;
use Symfony\Component\Dotenv\Dotenv;

require_once 'vendor/autoload.php';
ini_set('display_errors', 'stderr');

$envFile = __DIR__.'/.env';
if (file_exists($envFile)) {
    $dotEnv = new Dotenv();
    $dotEnv->load($envFile);
}

$env = Spiral\RoadRunner\Environment::fromGlobals();

$container = ContainerBuilder::Build(
    baseDir: Peregrine::BASE_DIR,
    cacheDir: Peregrine::CACHE_DIR
);

(new Peregrine($env, $container))->run();
