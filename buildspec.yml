version: 0.2
phases:
  install:
    runtime-version:
      php: 8.0

  build:
    commands:
      - echo Build started on `date`
      - echo Installing Composer Deps
      - composer install --no-ansi --no-dev --no-interaction --no-suggest --no-progress --no-scripts --optimize-autoloader --ignore-platform-reqs

  post_build:
    commands:
      - echo Build completed on `date`
      - mv '.'$CLUBSTER_ENV'.env' .env
#      - php build_container.php
      - find ./vendor -name ".git" -exec rm -rf {} \;

artifacts:
  name: 1.0.0.$CODEBUILD_BUILD_NUMBER.zip
  files:
    - cache/**/*
    - config/**/*
    - src/**/*
    - vendor/**/*
    - server.php
    - wren.php
    - composer.json
    - composer.lock
    - .env
    - appspec.yml
    - deploy/**/*
