<?php

use Carbon\Carbon;
use Mockingbird\Infrastructure\Falcon\FalconRequest;
use MongoDB\Client;
use Symfony\Component\Dotenv\Dotenv;

require_once 'vendor/autoload.php';

$envFile = __DIR__.'/.env';
if (file_exists($envFile)) {
    $dotEnv = new Dotenv();
    $dotEnv->load($envFile);
}

/** @var Client $mongo */
$mongo = new Client($_ENV['MONGO_DB_URI']);
$collection = $mongo->selectCollection($_ENV['MONGO_DB_NAME'],'message_queue');
$startTime = (new Carbon())->timestamp;
$cursor = $collection->find(['queue'=>'messaging','seen'=>false], [
    'cursorType' => MongoDB\Operation\Find::TAILABLE_AWAIT,
    'maxAwaitTimeMS' => 100,
]);

$cursor->rewind();

while (true) {
    if ($cursor->valid()) {
        $document = $cursor->current();
        echo sprintf('[%s] Processing Message Id', $document['message_id']).PHP_EOL;
        $resp = FalconRequest::execute('Messaging:Notifications:MessageSent', ['messageId'=>$document['message_id']]);
        if (FalconRequest::hasErrors()) {
            foreach (FalconRequest::getErrors() as $error) {
                echo sprintf('[%s] ERROR: %s', $document['message_id'],$error).PHP_EOL;
            }
        }
        echo sprintf('[%s] Response: %s', $document['message_id'], json_encode($resp)).PHP_EOL;
        $collection->updateOne(['_id'=>$document['_id']], ['$set'=>['seen'=>true]]);
        echo sprintf('[%s] Document Updated As Seen', $document['message_id']).PHP_EOL;
    }
    $cursor->next();
}
