<?php

use Clubster\Peregrine\Infrastructure\DependencyInjection\ContainerBuilder;
use Clubster\Peregrine\Infrastructure\Peregrine;
use Symfony\Component\Dotenv\Dotenv;

require_once 'vendor/autoload.php';

$envFile = __DIR__.'/.env';
if (file_exists($envFile)) {
    $dotEnv = new Dotenv();
    $dotEnv->load($envFile);
}

ContainerBuilder::Build(
    baseDir: Peregrine::BASE_DIR,
    cacheDir: Peregrine::CACHE_DIR,
    forceRebuild: true
);
