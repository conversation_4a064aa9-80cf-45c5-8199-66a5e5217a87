FROM public.ecr.aws/composer/composer:latest as composer
ARG COMPOSER_ARG=--no-dev
WORKDIR /app
COPY composer.* /app/
RUN set -xe && composer install --ignore-platform-reqs --no-dev --no-interaction --no-scripts
COPY . /app/
RUN composer dump-autoload $COMPOSER_ARG --optimize --classmap-authoritative

FROM ghcr.io/roadrunner-server/roadrunner:2024.3.5 as roadrunner

FROM php:8.3-cli as base
RUN apt-get update && apt-get install -y curl iputils-ping zlib1g-dev libpng-dev librabbitmq-dev libssh-dev git libzip-dev libicu-dev libonig-dev unzip vim
ADD https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/
RUN chmod +x /usr/local/bin/install-php-extensions
RUN install-php-extensions pgsql pdo_pgsql mongodb-1.14.0 pcntl bcmath gd redis excimer-1.2.3 sockets igbinary exif

ARG APP_ENV
ARG IMAGE_TAG
ARG COMMIT_HASH=unknown
ENV COMMIT_HASH=${COMMIT_HASH:-unknown}
WORKDIR /app
COPY . /app/
COPY --from=composer /app/vendor /app/vendor
COPY --from=roadrunner /usr/bin/rr /usr/local/bin/rr
RUN rm -rf /var/www/.env
RUN echo "$(date +%Y.%m.%d.%H.%M)" > /app/.version
RUN echo "${COMMIT_HASH}" > /app/.commit_hash

FROM base as release
EXPOSE 8088/tcp
EXPOSE 2114/tcp
CMD rr serve -c .rr.yaml
